name: Test

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_wallpapers
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip
          restore-keys: |
            ${{ runner.os }}-pip

      - name: Cache tox environments
        uses: actions/cache@v4
        with:
          path: .tox
          key: ${{ runner.os }}-tox
          restore-keys: |
            ${{ runner.os }}-tox

      - name: Install tox
        run: |
          python -m pip install --upgrade pip
          pip install tox

      - name: Run tests
        run: tox
        env:
          # PostgreSQL database configuration
          DJANGO_DB: postgresql
          DJANGO_DB_HOST: localhost
          DJANGO_DB_PORT: 5432
          DJANGO_DB_NAME: test_wallpapers
          DJANGO_DB_USER: postgres
          DJANGO_DB_PASSWORD: postgres

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          slug: Dog-Egg/oneproject-wallnest
