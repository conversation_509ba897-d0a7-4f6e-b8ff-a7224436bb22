FROM python:3.12.3-slim

# 安装依赖
RUN apt-get update && apt-get install -y --no-install-recommends git \
    && rm -rf /var/lib/apt/lists/*

# 创建 celery 用户
RUN useradd -m celery

WORKDIR /code

# 升级 pip
RUN pip install -U pip

# 复制依赖并安装
COPY requirements/django.txt requirements.txt
RUN pip install -r requirements.txt

# 修改属主
RUN chown -R celery:celery /code
# 拷贝项目文件并修改属主
COPY --chown=celery . /code

# 切换到 celery 用户
USER celery

ENV DJANGO_ENV=production

# 启动 celery worker
CMD [ "celery", "--app", "project", "worker", "-c", "1", "-l", "WARNING", "--beat" ]
