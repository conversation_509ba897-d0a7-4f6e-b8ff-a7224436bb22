## Help

所有可用命令都可以通过运行 `make` 命令查看

```sh
make
```


## Configurations

| Config                                  | Required   | Help   |
|-----------------------------------------|------------|--------|
| @env:CELERY_BROKER_URL                  | No         |        |
| @env:CLIP_SERVICE_ACCESS_TOKEN          | No         |        |
| @env:CLIP_SERVICE_URL                   | Yes        |        |
| @env:CLOUDFLARE_TURNSTILE_SECRET_KEY    | No         |        |
| @env:DJANGO_CORS_ALLOWED_ORIGINS        | No         |        |
| @env:DJANGO_CSRF_TRUSTED_ORIGINS        | No         |        |
| @env:DJANGO_DB                          | No         |        |
| @env:DJANGO_DB_HOST                     | Yes        |        |
| @env:DJANGO_DB_NAME                     | Yes        |        |
| @env:DJANGO_DB_PASSWORD                 | Yes        |        |
| @env:DJANGO_DB_PORT                     | Yes        |        |
| @env:DJANGO_DB_USER                     | Yes        |        |
| @env:DJANG<PERSON>_DEBUG                       | No         |        |
| @env:DJANGO_ENV                         | No         |        |
| @env:DJANGO_SENTRY_DSN                  | No         |        |
| @env:DJANGO_SERVICE                     | No         |        |
| @env:DJANGO_SILKY_INTERCEPT_PERCENT     | No         |        |
| @env:IMGPROXY_KEY                       | No         |        |
| @env:IMGPROXY_SALT                      | No         |        |
| @env:IMGPROXY_SOURCE_URL_ENCRYPTION_KEY | No         |        |
| @env:IMGPROXY_URL                       | Yes        |        |
| @env:QDRANT_API_KEY                     | No         |        |
| @env:QDRANT_URL                         | Yes        |        |
