import configat

from clip_service_client import ApiClient, Configuration, DefaultApi

CLIP_SERVICE_URL = configat.resolve("@env:CLIP_SERVICE_URL").rstrip("/")
CLIP_SERVICE_ACCESS_TOKEN = configat.resolve(
    "@env:CLIP_SERVICE_ACCESS_TOKEN", default=None
)

configuration = Configuration(host=CLIP_SERVICE_URL)
api_client = ApiClient(configuration)
if CLIP_SERVICE_ACCESS_TOKEN:
    api_client.set_default_header(
        "Authorization", f"Bearer {CLIP_SERVICE_ACCESS_TOKEN}"
    )

api = DefaultApi(api_client)
