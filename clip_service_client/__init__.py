# coding: utf-8

# flake8: noqa

"""
    FastAPI

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


__version__ = "1.0.0"

# Define package exports
__all__ = [
    "DefaultApi",
    "ApiResponse",
    "ApiClient",
    "Configuration",
    "OpenApiException",
    "ApiTypeError",
    "ApiValueError",
    "ApiKeyError",
    "ApiAttributeError",
    "ApiException",
    "EncodingImageResponse",
    "EncodingTextResponse",
    "HTTPValidationError",
    "ValidationError",
    "ValidationErrorLocInner",
]

# import apis into sdk package
from clip_service_client.api.default_api import DefaultApi as DefaultApi

# import ApiClient
from clip_service_client.api_response import Api<PERSON><PERSON>ponse as ApiResponse
from clip_service_client.api_client import Api<PERSON><PERSON> as ApiClient
from clip_service_client.configuration import Configuration as Configuration
from clip_service_client.exceptions import OpenApiException as OpenApiException
from clip_service_client.exceptions import ApiTypeError as ApiTypeError
from clip_service_client.exceptions import ApiValueError as ApiValueError
from clip_service_client.exceptions import ApiKeyError as ApiKeyError
from clip_service_client.exceptions import ApiAttributeError as ApiAttributeError
from clip_service_client.exceptions import ApiException as ApiException

# import models into sdk package
from clip_service_client.models.encoding_image_response import EncodingImageResponse as EncodingImageResponse
from clip_service_client.models.encoding_text_response import EncodingTextResponse as EncodingTextResponse
from clip_service_client.models.http_validation_error import HTTPValidationError as HTTPValidationError
from clip_service_client.models.validation_error import ValidationError as ValidationError
from clip_service_client.models.validation_error_loc_inner import ValidationErrorLocInner as ValidationErrorLocInner
