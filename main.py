import uvicorn

from project.asgi import application


class XForwardedPrefixMiddleware:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            headers = dict(scope.get("headers") or [])
            prefix = headers.get(b"x-forwarded-prefix")
            if prefix:
                scope["root_path"] = prefix.decode("UTF-8")
        await self.app(scope, receive, send)


application = XForwardedPrefixMiddleware(application)

if __name__ == "__main__":
    uvicorn.run(
        application,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        lifespan="off",
    )
