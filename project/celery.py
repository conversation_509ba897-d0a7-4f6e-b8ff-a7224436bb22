import os

from celery import Celery

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "project.settings")

app = Celery("wallnest")
app.config_from_object("django.conf:settings", namespace="CELERY")

app.autodiscover_tasks()


@app.on_after_configure.connect  # type: ignore
def setup_periodic_tasks(sender: Celery, **kwargs):
    # 由于 CLIP-Service 部署在 huggingface 上会自动休眠，所以需要定时调用防止休眠
    sender.add_periodic_task(3 * 60 * 60, make_clip_service_health)


@app.task
def make_clip_service_health():
    from clip_service import api

    api.health_health_get()
