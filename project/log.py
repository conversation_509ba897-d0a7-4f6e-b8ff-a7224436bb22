import logging

from django.conf import settings


class ConsoleFilter(logging.Filter):
    _level: int | None = None

    def filter(self, record: logging.LogRecord) -> bool:
        if settings.ENV != "production":
            return True

        if self._level is not None:
            return record.levelno >= self._level

        if record.levelno >= logging.WARNING:
            return True

        return False

    @classmethod
    def setLevel(cls, level: int):
        cls._level = level
