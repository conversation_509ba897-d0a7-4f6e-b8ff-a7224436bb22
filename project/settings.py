"""
Django settings for project project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import random
from pathlib import Path

import configat
import sentry_sdk
from configat import casting
from corsheaders.defaults import default_headers
from django.http import HttpRequest

from .env import load_env

ENV = load_env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-ssmqqmyu3!%!u)1d)pju@35mrfx$wlw+&_uh2=w%fa&)verwh-"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = configat.resolve(
    "@env:DJANGO_DEBUG", default=ENV == "development", cast=casting.boolean
)

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "wallpapers",
    "corsheaders",
    "silk",  # requirements: django-silk==5.4.0
    "users",
]

MIDDLEWARE = [
    "silk.middleware.SilkyMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",  # requirements: whitenoise
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "project.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases
if configat.resolve("@env:DJANGO_DB", None) == "postgresql":
    # requirements: psycopg[binary]
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": configat.resolve("@env:DJANGO_DB_NAME"),
            "USER": configat.resolve("@env:DJANGO_DB_USER"),
            "PASSWORD": configat.resolve("@env:DJANGO_DB_PASSWORD"),
            "HOST": configat.resolve("@env:DJANGO_DB_HOST"),
            "PORT": configat.resolve("@env:DJANGO_DB_PORT"),
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "zh-Hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = BASE_DIR / ".staticfiles"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


CSRF_TRUSTED_ORIGINS = configat.resolve(
    "@env:DJANGO_CSRF_TRUSTED_ORIGINS", [], lambda x: x.split(",")
)

SENTRY_DSN = configat.resolve("@env:DJANGO_SENTRY_DSN", None)
if SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
    )


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "root": {
        "handlers": ["console"],
    },
    "formatters": {
        "colored": {  # requirements: colorlog
            "()": "colorlog.ColoredFormatter",
            "format": "[%(levelname)s %(name)s] %(log_color)s%(message)s%(reset)s",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "bold_red",
            },
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "colored",
            "filters": ["console_filter"],
        }
    },
    "filters": {
        "console_filter": {
            "()": "project.log.ConsoleFilter",
        }
    },
    "loggers": {
        "django.db.backends": {
            "level": "DEBUG" if DEBUG else "WARNING",
        },
        "django": {
            "level": "ERROR" if ENV == "test" else "INFO",
        },
        "wallpapers": {
            "level": "DEBUG",
        },
    },
}

CF_TURNSTILE_HEADER = "CF-Turnstile-Response"

CORS_ALLOWED_ORIGINS = configat.resolve(
    "@env:DJANGO_CORS_ALLOWED_ORIGINS", [], cast=lambda x: x.split(",")
)
CORS_ALLOW_HEADERS = (
    *default_headers,
    CF_TURNSTILE_HEADER,
)

LOGIN_URL = "admin:login"
SILKY_AUTHENTICATION = True
SILKY_INTERCEPT_PERCENT = configat.resolve(
    "@env:DJANGO_SILKY_INTERCEPT_PERCENT", default=0.0, cast=float
)


def SILKY_INTERCEPT_FUNC(request: HttpRequest) -> bool:
    if request.path_info.startswith("/static/"):
        return False
    if random.random() > SILKY_INTERCEPT_PERCENT / 100.0:
        return False
    return True


if DEBUG:
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.filebased.FileBasedCache",
            "LOCATION": BASE_DIR / ".cache",
        }
    }

CELERY_BROKER_URL = configat.resolve("@env:CELERY_BROKER_URL", None)
CELERY_TASK_ALWAYS_EAGER = ENV != "production"
