from dataclasses import dataclass, field

import configat
from django.contrib import admin
from django.shortcuts import redirect, render
from django.urls import NoReverseMatch, include, path, reverse

admin.site.site_title = "Wallnest 管理"
admin.site.site_header = "Walladmin"


@dataclass(kw_only=True)
class SiteMapNode:
    title: str
    urlname: str
    children: list["SiteMapNode"] = field(default_factory=list)


SITE_MAP_NODES = [
    SiteMapNode(
        title="APIs",
        urlname="wallpapers-api:apidoc",
    ),
    SiteMapNode(title="Admin", urlname="admin:index"),
    SiteMapNode(
        title="Silk",
        urlname="silk:summary",
    ),
]


def sitemap_view(request):
    def parse_sitemap(nodes: list[SiteMapNode]):
        if not nodes:
            return []

        def inner():
            for node in nodes:
                try:
                    url = reverse(node.urlname)
                except NoReverseMatch:
                    continue
                yield {
                    "title": node.title,
                    "url": url,
                    "children": list(parse_sitemap(node.children)),
                }

        return list(inner())

    return render(
        request,
        "index.html",
        context={"site_map": parse_sitemap(SITE_MAP_NODES)},
    )


SERVICE = configat.resolve("@env:DJANGO_SERVICE", None)
assert SERVICE in {None, "admin", "wallpapers-api"}

silk_path = path("silk/", include("silk.urls", namespace="silk"))

if SERVICE == "admin":
    urlpatterns = [
        path("", lambda _: redirect(reverse("admin:index"))),
        path("admin/", admin.site.urls),
        silk_path,
    ]
elif SERVICE == "wallpapers-api":
    urlpatterns = [
        path("", include("wallpapers.urls")),
    ]
else:
    # development urls
    urlpatterns = [
        path("", sitemap_view),
        path("admin/", admin.site.urls),
        path("api/", include("wallpapers.urls")),
        silk_path,
    ]
