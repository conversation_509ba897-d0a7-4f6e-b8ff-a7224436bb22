from django.contrib import admin
from django.db.models.query import QuerySet
from django.http import HttpRequest

from . import models


@admin.register(models.Role)
class RoleAdmin(admin.ModelAdmin):
    filter_horizontal = ["users"]


@admin.register(models.RoleAPIPermission)
class RoleAPIPermissionAdmin(admin.ModelAdmin):
    list_display = ["role__name", "operation_id"]

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("role")
