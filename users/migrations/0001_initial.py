# Generated by Django 5.2.3 on 2025-08-10 07:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, verbose_name="角色名称")),
                (
                    "users",
                    models.ManyToManyField(
                        to=settings.AUTH_USER_MODEL, verbose_name="用户"
                    ),
                ),
            ],
            options={
                "verbose_name": "角色",
                "verbose_name_plural": "角色",
            },
        ),
        migrations.CreateModel(
            name="RoleAPIPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "operation_id",
                    models.<PERSON>r<PERSON>ield(max_length=255, verbose_name="接口ID"),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.role",
                        verbose_name="角色",
                    ),
                ),
            ],
            options={
                "verbose_name": "角色接口权限",
                "verbose_name_plural": "角色接口权限",
                "constraints": [
                    models.UniqueConstraint(
                        fields=("role", "operation_id"), name="unique_role_operation"
                    )
                ],
            },
        ),
    ]
