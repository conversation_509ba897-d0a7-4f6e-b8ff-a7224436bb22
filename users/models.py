from django.contrib.auth.models import User
from django.db import models


class Role(models.Model):
    name = models.CharField(max_length=50, verbose_name="角色名称")
    users = models.ManyToManyField(User, verbose_name="用户")

    class Meta:
        verbose_name = "角色"
        verbose_name_plural = "角色"

    def __str__(self) -> str:
        return self.name


class RoleAPIPermission(models.Model):
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="角色")
    role_id: int
    operation_id = models.CharField(max_length=255, verbose_name="接口ID")

    class Meta:
        verbose_name = "角色接口权限"
        verbose_name_plural = "角色接口权限"
        constraints = [
            models.UniqueConstraint(
                fields=["role", "operation_id"], name="unique_role_operation"
            )
        ]
