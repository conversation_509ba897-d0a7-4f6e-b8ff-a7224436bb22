from django.contrib.auth.models import User
from django.db.models.signals import m2m_changed, post_delete, post_save
from django.dispatch import receiver

from wallpapers.auth import user_permissions

from .models import Role, RoleAPIPermission


@receiver(m2m_changed, sender=Role.users.through)
def clear_user_permissions_cache_on_role_users_change(
    sender, instance, action, pk_set, **kwargs
):
    """当角色的用户关系发生变化时，清除相关用户的权限缓存"""

    # 在clear操作前，先保存用户列表
    if action == "pre_clear" and isinstance(instance, Role):
        for user in instance.users.all():
            user_permissions.clear_cache(user)
        return

    if action in ["post_add", "post_remove"]:
        # 处理add和remove操作
        if pk_set:
            if isinstance(instance, Role):
                users = User.objects.filter(pk__in=pk_set).all()
                for user in users:
                    user_permissions.clear_cache(user)
            elif isinstance(instance, User):
                # instance是User，pk_set是Role的ID
                user_permissions.clear_cache(instance)


@receiver([post_save, post_delete], sender=RoleAPIPermission)
def clear_user_permissions_cache_on_role_permission_change(sender, instance, **kwargs):
    """当角色API权限发生变化时，清除相关用户的权限缓存"""
    # 清除该角色下所有用户的权限缓存
    for user in instance.role.users.all():
        user_permissions.clear_cache(user)


@receiver(post_delete, sender=Role)
def clear_user_permissions_cache_on_role_delete(sender, instance, **kwargs):
    """当角色被删除时，清除相关用户的权限缓存"""
    # 在删除前，相关用户已经通过m2m_changed信号处理了
    # 这里主要是为了确保完整性
    pass  # pragma: no cover
