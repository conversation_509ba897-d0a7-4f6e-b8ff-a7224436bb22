from django.contrib.auth.models import User
from django.core.cache import cache
from django.test import TestCase

from wallpapers.auth import user_permissions

from .models import Role, RoleAPIPermission


class UserPermissionsIntegrationTest(TestCase):
    """集成测试：验证用户权限缓存在实际场景中的工作情况"""

    def setUp(self):
        """设置测试数据"""
        cache.clear()  # 清除所有缓存
        self.user = User.objects.create_user(username="testuser", password="pass")
        self.role = Role.objects.create(name="测试角色")

    def test_user_permissions_cache_lifecycle(self):
        """测试用户权限缓存的完整生命周期"""

        # 1. 初始状态：用户没有权限
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, set())

        # 2. 给角色添加权限
        permission1 = RoleAPIPermission.objects.create(
            role=self.role, operation_id="test_operation_1"
        )

        # 3. 将用户添加到角色
        self.role.users.add(self.user)

        # 4. 验证用户现在有权限了（缓存应该被清除并重新加载）
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"test_operation_1"})

        # 5. 添加更多权限
        RoleAPIPermission.objects.create(
            role=self.role, operation_id="test_operation_2"
        )

        # 6. 验证权限更新（缓存应该被清除）
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"test_operation_1", "test_operation_2"})

        # 7. 删除一个权限
        permission1.delete()

        # 8. 验证权限减少（缓存应该被清除）
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"test_operation_2"})

        # 9. 从角色中移除用户
        self.role.users.remove(self.user)

        # 10. 验证用户没有权限了（缓存应该被清除）
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, set())

    def test_multiple_roles_permissions(self):
        """测试用户属于多个角色时的权限管理"""

        # 创建第二个角色
        role2 = Role.objects.create(name="测试角色2")

        # 给两个角色分别添加不同的权限
        RoleAPIPermission.objects.create(role=self.role, operation_id="role1_operation")
        RoleAPIPermission.objects.create(role=role2, operation_id="role2_operation")

        # 用户加入第一个角色
        self.role.users.add(self.user)
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"role1_operation"})

        # 用户加入第二个角色
        role2.users.add(self.user)
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"role1_operation", "role2_operation"})

        # 从第一个角色移除用户
        self.role.users.remove(self.user)
        permissions = user_permissions(self.user)
        self.assertEqual(permissions, {"role2_operation"})

    def test_role_users_clear_operation(self):
        """测试角色用户清空操作"""

        # 创建多个用户
        user2 = User.objects.create_user(username="testuser2", password="pass")
        user3 = User.objects.create_user(username="testuser3", password="pass")

        # 给角色添加权限
        RoleAPIPermission.objects.create(role=self.role, operation_id="test_operation")

        # 将所有用户添加到角色
        self.role.users.add(self.user, user2, user3)

        # 验证所有用户都有权限
        for user in [self.user, user2, user3]:
            permissions = user_permissions(user)
            self.assertEqual(permissions, {"test_operation"})

        # 清空角色的所有用户
        self.role.users.clear()

        # 验证所有用户都没有权限了
        for user in [self.user, user2, user3]:
            permissions = user_permissions(user)
            self.assertEqual(permissions, set())
