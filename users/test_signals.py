from unittest.mock import patch

from django.contrib.auth.models import User
from django.test import TestCase

from wallpapers.auth import user_permissions

from .models import Role, RoleAPIPermission


class UserPermissionsCacheSignalsTest(TestCase):
    """测试用户权限缓存清除信号"""

    def setUp(self):
        """设置测试数据"""
        self.user1 = User.objects.create_user(username="user1", password="pass")
        self.user2 = User.objects.create_user(username="user2", password="pass")
        self.role = Role.objects.create(name="测试角色")

    @patch.object(user_permissions, "clear_cache")
    def test_role_users_add_clears_cache(self, mock_clear_cache):
        """测试添加用户到角色时清除缓存"""
        # 添加用户到角色
        self.role.users.add(self.user1)

        # 验证缓存被清除
        mock_clear_cache.assert_called_with(self.user1)

    @patch.object(user_permissions, "clear_cache")
    def test_role_users_remove_clears_cache(self, mock_clear_cache):
        """测试从角色移除用户时清除缓存"""
        # 先添加用户
        self.role.users.add(self.user1)
        mock_clear_cache.reset_mock()

        # 移除用户
        self.role.users.remove(self.user1)

        # 验证缓存被清除
        mock_clear_cache.assert_called_with(self.user1)

    @patch.object(user_permissions, "clear_cache")
    def test_role_users_clear_clears_cache(self, mock_clear_cache):
        """测试清空角色用户时清除缓存"""
        # 添加多个用户
        self.role.users.add(self.user1, self.user2)
        mock_clear_cache.reset_mock()

        # 清空所有用户
        self.role.users.clear()

        # 验证所有用户的缓存都被清除
        self.assertTrue(mock_clear_cache.called)

    @patch.object(user_permissions, "clear_cache")
    def test_role_users_set_clears_cache(self, mock_clear_cache):
        """测试设置角色用户时清除缓存"""
        # 先添加一个用户
        self.role.users.add(self.user1)
        mock_clear_cache.reset_mock()

        # 设置新的用户列表
        self.role.users.set([self.user2])

        # 验证缓存被清除
        self.assertTrue(mock_clear_cache.called)

    @patch.object(user_permissions, "clear_cache")
    def test_role_api_permission_create_clears_cache(self, mock_clear_cache):
        """测试创建角色API权限时清除缓存"""
        # 给角色添加用户
        self.role.users.add(self.user1, self.user2)
        mock_clear_cache.reset_mock()

        # 创建角色API权限
        RoleAPIPermission.objects.create(role=self.role, operation_id="test_operation")

        # 验证角色下所有用户的缓存都被清除
        expected_calls = [
            mock_clear_cache.call_args_list[i][0][0]
            for i in range(len(mock_clear_cache.call_args_list))
        ]
        self.assertIn(self.user1, expected_calls)
        self.assertIn(self.user2, expected_calls)

    @patch.object(user_permissions, "clear_cache")
    def test_role_api_permission_update_clears_cache(self, mock_clear_cache):
        """测试更新角色API权限时清除缓存"""
        # 给角色添加用户并创建权限
        self.role.users.add(self.user1)
        permission = RoleAPIPermission.objects.create(
            role=self.role, operation_id="test_operation"
        )
        mock_clear_cache.reset_mock()

        # 更新权限
        permission.operation_id = "updated_operation"
        permission.save()

        # 验证缓存被清除
        mock_clear_cache.assert_called_with(self.user1)

    @patch.object(user_permissions, "clear_cache")
    def test_role_api_permission_delete_clears_cache(self, mock_clear_cache):
        """测试删除角色API权限时清除缓存"""
        # 给角色添加用户并创建权限
        self.role.users.add(self.user1, self.user2)
        permission = RoleAPIPermission.objects.create(
            role=self.role, operation_id="test_operation"
        )
        mock_clear_cache.reset_mock()

        # 删除权限
        permission.delete()

        # 验证角色下所有用户的缓存都被清除
        expected_calls = [
            mock_clear_cache.call_args_list[i][0][0]
            for i in range(len(mock_clear_cache.call_args_list))
        ]
        self.assertIn(self.user1, expected_calls)
        self.assertIn(self.user2, expected_calls)

    @patch.object(user_permissions, "clear_cache")
    def test_multiple_roles_user_cache_clearing(self, mock_clear_cache):
        """测试用户属于多个角色时的缓存清除"""
        # 创建另一个角色
        role2 = Role.objects.create(name="测试角色2")

        # 用户加入两个角色
        self.role.users.add(self.user1)
        role2.users.add(self.user1)
        mock_clear_cache.reset_mock()

        # 给第一个角色添加权限
        RoleAPIPermission.objects.create(role=self.role, operation_id="operation1")

        # 验证用户缓存被清除
        mock_clear_cache.assert_called_with(self.user1)
        mock_clear_cache.reset_mock()

        # 给第二个角色添加权限
        RoleAPIPermission.objects.create(role=role2, operation_id="operation2")

        # 验证用户缓存再次被清除
        mock_clear_cache.assert_called_with(self.user1)

    @patch.object(user_permissions, "clear_cache")
    def test_role_without_users_no_cache_clearing(self, mock_clear_cache):
        """测试没有用户的角色不会触发缓存清除"""
        # 创建没有用户的角色权限
        RoleAPIPermission.objects.create(role=self.role, operation_id="test_operation")

        # 验证没有调用缓存清除（因为角色没有用户）
        mock_clear_cache.assert_not_called()

    @patch.object(user_permissions, "clear_cache")
    def test_user_role_operations_clear_cache(self, mock_clear_cache):
        """测试从用户角度操作角色关系时清除缓存"""
        # 从用户的角度添加角色（这会触发instance是User的情况）
        self.user1.role_set.add(self.role)

        # 验证用户缓存被清除
        mock_clear_cache.assert_called_with(self.user1)
        mock_clear_cache.reset_mock()

        # 从用户的角度移除角色
        self.user1.role_set.remove(self.role)

        # 验证用户缓存被清除
        mock_clear_cache.assert_called_with(self.user1)
