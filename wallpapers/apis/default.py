import secrets
import typing
import uuid
from typing import Iterable, Mapping

import httpx
import validators
import zangar as z
from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.cache import cache
from django.db.models import F, Model, OuterRef, Q, Subquery, Window
from django.db.models.functions import RowNumber
from django.db.models.query import QuerySet
from django.http import Http404, HttpRequest, HttpResponse, JsonResponse
from django.urls import reverse_lazy

import openapi
from wallpapers import models
from wallpapers.auth import generate_access_token, required_jwt, user_permissions
from wallpapers.common import SizedForEnum
from wallpapers.utils import cloudflare
from wallpapers.utils import query_on_demand as qd
from wallpapers.utils.colors import ColorFamily
from wallpapers.utils.datastructures import ChainObject
from wallpapers.utils.imgproxy import get_imgproxy_url

from .base import APIView
from .common import s_in_topic, s_query_image_sizes

M = typing.TypeVar("M", bound=Model)


def _paginate(queryset: QuerySet[M], page: int, page_size: int) -> QuerySet[M]:
    return queryset[(page - 1) * page_size : page * page_size]


def _default_images(url: str):
    return {"default": get_imgproxy_url(url, width=500, height=500)}


def _images_getter(o: models.WallpaperImage | Mapping):
    if isinstance(o, Mapping):
        if "images" in o:
            return o["images"]
        return _default_images(o["url"])
    else:
        if images := o.images:
            return images
        return _default_images(o.url)


def _conditional_decorator(conditional: bool, decorator: typing.Callable):
    """条件装饰器, 仅在条件成立时应用装饰器。"""

    def wrapper(func):
        if conditional:
            return decorator(func)
        return func

    return wrapper


_uploader_struct = z.struct(
    {
        "name": z.str(),
    }
)
_image_url_faker = {
    "helpers.arrayElement": [
        [
            "https://dummyimage.com/300x400",
            "https://dummyimage.com/800x200",
        ]
    ]
}
_wallpaper_struct = z.struct(
    {
        "images": z.field(
            z.ensure(
                lambda x: isinstance(x, dict),
                meta={
                    "oas": {
                        "type": "object",
                        "properties": {
                            "default": {
                                "type": "string",
                                "x-faker": _image_url_faker,
                            }
                        },
                        "additionalProperties": {
                            "type": "string",
                            "x-faker": _image_url_faker,
                        },
                        "description": "图片列表，当未提供任何 size 时，将默认填充一个 default 字段",
                    }
                },
            ),
            getter=_images_getter,
        ),
        "format": z.str(),
        "width": z.int(),
        "height": z.int(),
        "filesize": z.int(meta={"oas": {"description": "单位为字节"}}),
        "content_md5": z.str(),
    }
)
_wallpaper_with_id_struct = z.struct(
    {
        "id": z.int(),
        **_wallpaper_struct.fields,
    }
)
_dash_wallpaper_struct = z.struct(
    {
        "id": z.int(),
        "url": z.str(),
        **_wallpaper_struct.fields,
    }
)
_wallpaper_with_uploader_struct = z.struct(
    {
        **_wallpaper_struct.fields,
        "uploader": z.field(
            _uploader_struct,
            getter=lambda o: getattr(o, "uploader"),
        ),
    }
)


def _attach_uploaders(queryset: Iterable[models.WallpaperImage]):
    """为 queryset 中的每个对象附加 uploader 信息

    要求 queryset 中的每个对象都设置上 uploader_id 字段。

    弃用: 改用 _attach_uploader2 函数，快捷方便。
    """
    uploader_ids = [getattr(x, "uploader_id") for x in queryset]
    uploaders = models.Uploader.objects.filter(id__in=uploader_ids)
    uploader_dict = {x.pk: x for x in uploaders}
    for item in queryset:
        item.uploader = uploader_dict[item.uploader_id]  # type: ignore
    return queryset


def _attach_uploaders2(wallpapers: Iterable[models.WallpaperImage]):
    """为每个 wallpaper 附加上 uploader 信息

    无需提前设置 uploader_id 字段
    """
    earliest_uploader = models.UploaderWallpaper.objects.filter(
        wallpaper=OuterRef("wallpaper")
    ).order_by("-pk")
    uploader_wallpapers = models.UploaderWallpaper.objects.filter(
        wallpaper__in=wallpapers, id__in=Subquery(earliest_uploader.values("pk")[:1])
    ).select_related("uploader")
    uploader_dict = {x.wallpaper_id: x.uploader for x in uploader_wallpapers}
    for item in wallpapers:
        item.uploader = uploader_dict.get(item.pk, models.Uploader(name="elephant31"))
    return wallpapers


def _split_size(size: str) -> tuple[int, int]:
    width, height = map(int, size.split("x", 1))
    return width, height


_s_page = openapi.s_query(name="page", schema=z.to.int().gte(1).lte(1000), py_default=1)
_s_page_size = openapi.s_query(
    name="page_size", schema=z.to.int().gte(1).lte(1000), py_default=10
)
_s_sized_for = openapi.s_query(
    name="sized_for",
    schema=z.str()
    .ensure(
        lambda x: x in SizedForEnum,
        meta={
            "oas": {
                "enum": list(SizedForEnum),
            }
        },
    )
    .transform(SizedForEnum),
    py_default=None,
    description="[filter] 根据尺寸进行过滤",
)


_SQL_MAX_INT = 2**63 - 1
_SQL_MIN_INT = -(2**63)


def _apply_sized_for(
    queryset: QuerySet[models.WallpaperImage], sized_for: SizedForEnum
):
    if sized_for == SizedForEnum.DESKTOP:
        queryset = queryset.filter(aspect_ratio__gte=1.2)
    elif sized_for == SizedForEnum.MOBILE:
        queryset = queryset.filter(aspect_ratio__lt=1.2)
    return queryset


class WallpaperListView(APIView):
    __get_response_schema = z.struct(
        {
            "wallpapers": z.to.list(_wallpaper_with_uploader_struct),
            "current_page": z.int(),
            "current_page_size": z.int(),
            "total": z.int(),
        }
    )

    @openapi.declare(
        summary="获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。",
        description="图片为等比例缩放",
        deprecated=True,
    )
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        sizes=s_query_image_sizes,
        page=_s_page,
        page_size=_s_page_size,
        filter_sized_for=_s_sized_for,
        filter_color=openapi.s_query(
            name="color",
            schema=z.str(),
            py_default=None,
            description="[filter] 根据色系进行过滤",
        ),
        filter_tag=openapi.s_query(
            name="tag",
            schema=z.str(),
            py_default=None,
            description="[filter] 根据标签进行过滤",
        ),
    ):
        queryset = (
            models.WallpaperImage.objects.annotate(
                uploader_id=Subquery(
                    models.UploaderWallpaper.objects.filter(
                        wallpaper=OuterRef("pk")
                    ).values("uploader_id")[:1]
                )
            )
            .filter(uploader_id__isnull=False)
            .order_by("-id")
        )

        # filter
        if filter_sized_for is not None:
            queryset = _apply_sized_for(queryset, filter_sized_for)
        if filter_color is not None:
            queryset = queryset.filter(imagecolorfamily__color_family=filter_color)
        if filter_tag is not None:
            queryset = queryset.filter(
                cliptag__name=filter_tag,
                cliptag__score__gte=models.TAG_SCORE_THRESHOLD,
            )

        # set images
        for item in queryset:
            if sizes:
                item.set_images(sizes)

        results = _paginate(queryset, page, page_size)
        results = _attach_uploaders(results)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "wallpapers": results,
                    "current_page": page,
                    "current_page_size": page_size,
                    "total": queryset.count(),
                }
            ),
        )


class ImageAPI(APIView):
    @openapi.declare(summary="获取原始图片")
    @openapi.path("key", schema=z.str())
    @openapi.response(200, content={"image/*": openapi.MediaType()})
    @cloudflare.check_tunstile
    async def get(self, request, key):
        try:
            wallpaper = await models.WallpaperImage.objects.aget(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        async with httpx.AsyncClient(timeout=30) as client:
            r = await client.get(wallpaper.url)
            if not r.is_success:
                return HttpResponse(status=404)  # TODO: 图片不可用，该如何处理
            return HttpResponse(r.content, content_type=r.headers["content-type"])


class ColorCategoryAPI(APIView):
    __get_schema = z.struct(
        {
            "colors": z.to.list(
                z.struct(
                    {
                        "value": z.str(),
                        "RGB": z.str(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_schema)},
    )
    @openapi.declare(deprecated=True)
    async def get(self, request):
        return JsonResponse(
            self.__get_schema.parse(
                {
                    "colors": (x.asdict() for x in ColorFamily),
                }
            )
        )


class TagCategoryAPI(APIView):
    __get_response_schema = z.struct(
        {
            "tags": z.to.list(
                z.struct(
                    {
                        "name": z.str(),
                        "posterUrl": z.str() | z.none(),
                    }
                )
            )
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(deprecated=True)
    def get(self, request):
        names = ["城市", "自然", "动物", "艺术"]

        # 获取每个标签最高分的图片作为 poster，使用窗口函数
        queryset = (
            models.CLIPTag.qualified.filter(name__in=names)
            .annotate(
                row_num=Window(
                    expression=RowNumber(),
                    partition_by=F("name"),
                    order_by=[F("score").desc()],
                )
            )
            .filter(row_num=1)
            .select_related("wallpaper")
        )

        tagname_to_posterURL = {}
        for tag in queryset:
            tagname_to_posterURL[tag.name] = get_imgproxy_url(
                tag.wallpaper.url, width=200, height=200
            )

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "tags": [
                        {
                            "name": name,
                            "posterUrl": tagname_to_posterURL.get(name),
                        }
                        for name in names
                    ]
                }
            )
        )


class RelatedWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "related": z.to.list(_wallpaper_with_uploader_struct),
        }
    )

    @openapi.declare(summary="获取当前壁纸的相关其他壁纸 (固定数量的)")
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=__get_response_schema),
        },
    )
    @openapi.response(404)
    @openapi.path("key", schema=z.str(), description="使用 content_md5 值")
    @openapi.query(
        "num",
        schema=z.to.int(),
        required=False,
        description="返回的数据量最大条数",
    )
    @openapi.declare(deprecated=True)
    @openapi.apply_signature
    def get(
        self,
        request,
        key,
        num: int = 20,
        sizes=s_query_image_sizes,
    ):
        try:
            current_wallpaper = models.WallpaperImage.objects.get(content_md5=key)
        except models.WallpaperImage.DoesNotExist:
            return HttpResponse(status=404)

        related_wallpapers: list[models.WallpaperImage] = []
        tags = current_wallpaper.cliptag_set.order_by("-score").all()
        for tag in tags:
            # 获取有上传者的相关壁纸
            items = (
                models.CLIPTag.qualified.select_related("wallpaper")
                .filter(name=tag.name)
                .exclude(
                    wallpaper__in=[current_wallpaper, *related_wallpapers]
                )  # 不包括自己和已经查出的壁纸
                .annotate(
                    uploader_id=Subquery(
                        models.UploaderWallpaper.objects.filter(
                            wallpaper=OuterRef("wallpaper")
                        ).values("uploader")[:1]
                    )
                )
                .filter(uploader_id__isnull=False)  # 只包含有上传者的壁纸
                .order_by("-score")[: num - len(related_wallpapers)]
            )
            for x in items:
                if sizes:
                    x.wallpaper.set_images(sizes)
                x.wallpaper.uploader_id = x.uploader_id  # type: ignore
                related_wallpapers.append(x.wallpaper)
            if len(related_wallpapers) == num:
                break

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "related": _attach_uploaders(related_wallpapers),
                }
            )
        )


_client_topic_struct = z.struct(
    {
        "id": z.int(),
        "title": z.str(),
        "topic_id": z.int(),
    }
)


_s_path_client_id = openapi.s_path(
    name="client_id", schema=z.str().transform(lambda x: uuid.UUID(x, version=4))
)


class ClientTopicListAPI(APIView):
    __get_response_schema = z.struct({"client_topics": z.to.list(_client_topic_struct)})

    @openapi.query("group", schema=z.str(), required=False)
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(deprecated=True)
    @openapi.apply_signature
    def get(
        self,
        request,
        client_id=_s_path_client_id,
        after=openapi.s_query(
            schema=z.to.int(),
            py_default=None,
            description="使用 client_topic.id，而不是 topic_id",
        ),
        limit=openapi.s_query(schema=z.to.int().gte(1), py_default=20),
    ):
        client_topics = models.ClientTopic.objects.filter(client_id=client_id).order_by(
            "-pk"
        )

        if after is not None:
            client_topics = client_topics.filter(pk__lt=after)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "client_topics": client_topics[:limit],
                }
            )
        )


class ClientTopicWallpaperListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "client_topic": _client_topic_struct,
            "wallpapers": z.to.list(_wallpaper_with_uploader_struct),
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.response(404)
    @openapi.declare(deprecated=True)
    @openapi.apply_signature
    def get(
        self,
        request,
        client_id=_s_path_client_id,
        topic_id=openapi.s_path(name="topic_id", schema=z.to.int()),
        sizes=s_query_image_sizes,
    ):
        try:
            client_topic = models.ClientTopic.objects.get(
                topic_id=topic_id, client_id=client_id
            )
        except models.ClientTopic.DoesNotExist:
            raise Http404

        topic_wallpapers = (
            models.TopicWallpaper.objects.filter(topic=client_topic.topic_id)
            .select_related("wallpaper")
            .order_by("-pk")
        )

        wallpapers = [i.wallpaper for i in topic_wallpapers]
        if sizes:
            for item in wallpapers:
                item.set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "client_topic": client_topic,
                    "wallpapers": _attach_uploaders2(wallpapers),
                }
            )
        )


_REFRESH_TOKEN_COOKIE_KEY = "refresh_token"
_REFRESH_TOKEN_CACHE_KEY = "refresh_token:%s"
_auto_token_response_schema = z.struct(
    {
        "access_token": z.str(),
    }
)
_REFRESH_TOKEN_COOKIE_PATH = (
    "/" if settings.DEBUG else reverse_lazy("wallpapers-api:DashRefreshTokenAPI")
)


class DashTokenAPI(APIView):
    __request_meida_type = openapi.MediaType(
        schema=z.struct(
            {
                "username": z.str(),
                "password": z.str(meta={"oas": {"format": "password"}}),
            }
        )
    )

    @openapi.body(
        "body",
        content={
            "application/x-www-form-urlencoded": __request_meida_type,
            "application/json": __request_meida_type,
        },
    )
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=_auto_token_response_schema)
        },
    )
    @openapi.response(401)
    @cloudflare.check_tunstile
    @openapi.declare(tags=["dashboard"])
    def post(self, request, body):
        user = authenticate(
            request, username=body["username"], password=body["password"]
        )
        if user is None:
            return HttpResponse(status=401)

        refresh_token = secrets.token_urlsafe(64)
        refresh_token_timeout = 60 * 60 * 24 * 30
        cache.set(
            _REFRESH_TOKEN_CACHE_KEY % refresh_token, user.pk, refresh_token_timeout
        )
        response = JsonResponse({"access_token": generate_access_token(user.pk)})
        response.set_cookie(
            key=_REFRESH_TOKEN_COOKIE_KEY,
            value=refresh_token,
            httponly=True,
            path=_REFRESH_TOKEN_COOKIE_PATH,
            secure=not settings.DEBUG,
            max_age=refresh_token_timeout,
        )
        return response


class DashRefreshTokenAPI(APIView):
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(schema=_auto_token_response_schema)
        },
    )
    @openapi.response(401)
    @openapi.declare(tags=["dashboard"])
    def get(self, request: HttpRequest):
        refresh_token = request.COOKIES.get(_REFRESH_TOKEN_COOKIE_KEY)
        if not refresh_token:
            return HttpResponse(status=401)
        user_id = cache.get(_REFRESH_TOKEN_CACHE_KEY % refresh_token, default=None)
        if user_id is None:
            return HttpResponse(status=401)
        return JsonResponse({"access_token": generate_access_token(user_id)})


class DashRevokeTokenAPI(APIView):
    @openapi.response(200)
    @openapi.declare(tags=["dashboard"])
    def get(self, request: HttpRequest):
        refresh_token = request.COOKIES.get(_REFRESH_TOKEN_COOKIE_KEY)
        if refresh_token:
            cache.delete(_REFRESH_TOKEN_CACHE_KEY % refresh_token)
        response = HttpResponse(status=200)
        response.delete_cookie(
            _REFRESH_TOKEN_COOKIE_KEY, path=_REFRESH_TOKEN_COOKIE_PATH
        )
        return response


class DashUploadWallapersAPI(APIView):
    __post_operation_id = "postUploadWallpapers"

    @openapi.response(200)
    @openapi.declare(tags=["dashboard"], operationId=__post_operation_id)
    @required_jwt(permission=__post_operation_id)
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(
                schema=z.struct(
                    {
                        "urls": z.list(
                            z.str().ensure(lambda x: validators.url(x) is True)
                        ).ensure(lambda x: len(x) > 0, message="最少要提供一个 URL")
                    }
                )
            )
        },
    )
    def post(self, request, body):
        from wallpapers.tasks import create_wallpaper

        for url in set(body["urls"]):
            create_wallpaper.delay(url)
        return JsonResponse(body)


_topic_struct = z.struct(
    {
        "id": z.int(),
        "comment": z.str(),
    }
)

_post_topic_struct = z.struct(
    z.omit_fields(_topic_struct.fields, ["id"]),
)
_put_topic_struct = z.struct(z.required_fields(_post_topic_struct.fields))


def _s_fields_func(model: type[qd.Model]) -> qd.FieldSelector:
    fields: list[str] = qd.flatten(model)
    fields_set = set(fields)
    return openapi.s_query(
        name="fields",
        schema=z.to.list(
            z.str(meta={"oas": {"enum": fields}}).ensure(lambda x: x in fields_set)
        ).transform(lambda x: qd.FieldSelector(x) if x else qd.FieldSelector(fields)),
        py_default=qd.FieldSelector(fields),
    )


class _PaginationModel(qd.Model):
    page = qd.Field(z.int().gte(1))
    page_size = qd.Field(z.int().gte(1))
    total = qd.Field(z.int().gte(0))


class _ClientModel(qd.Model):
    id = qd.Field(z.to.str())
    name = qd.Field(z.str())


class _TopicModel(qd.Model):
    id = qd.Field(z.int())
    comment = qd.Field(z.str())


class _ClientListModel(_PaginationModel):
    results = _ClientModel(many=True)


_paginator_fields = {
    "page": z.int().gte(1),
    "page_size": z.int().gte(1),
    "total": z.int().gte(0),
}


class DashTopicListAPI(APIView):
    __get_response_schema = z.struct(
        {"results": z.to.list(_topic_struct), **_paginator_fields}
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        page=_s_page,
        page_size=_s_page_size,
        exclude_wallpaper_id=openapi.s_query(
            schema=z.to.int().lte(_SQL_MAX_INT).gte(_SQL_MIN_INT),
            description="排除含有指定壁纸的专题",
            py_default=None,
        ),
        in_client=openapi.s_query(
            name="in_client",
            schema=z.ensure(
                lambda x: x in {"true", "false"}, meta={"oas": {"type": "boolean"}}
            ).transform(lambda x: bool(x == "true")),
            py_default=None,
        ),
    ):
        queryset = models.Topic.objects.order_by("-pk")

        if exclude_wallpaper_id is not None:
            queryset = queryset.exclude(
                topicwallpaper__wallpaper_id=exclude_wallpaper_id
            )
        if in_client is not None:
            if in_client:
                queryset = queryset.filter(clienttopic__isnull=False)
            else:
                queryset = queryset.filter(clienttopic__isnull=True)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": _paginate(queryset, page, page_size),
                    "page": page,
                    "page_size": page_size,
                    "total": queryset.count(),
                }
            )
        )

    @openapi.response(
        200, content={"application/json": openapi.MediaType(schema=_topic_struct)}
    )
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(schema=_post_topic_struct),
        },
    )
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    def post(self, request, body):
        obj = models.Topic.objects.create(**body)
        return JsonResponse(_topic_struct.parse(obj))


_path_topic_id = openapi.path("topic_id", schema=z.to.int())
_s_path_topic_id = openapi.s_path(name="topic_id", schema=z.to.int())


def _get_topic(topic_id):
    try:
        return models.Topic.objects.get(pk=topic_id)
    except models.Topic.DoesNotExist:
        raise Http404


class DashTopicDetailAPI(APIView):
    __get_response_schema = qd.struct(_TopicModel, optional=True)

    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.declare(tags=["dashboard"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @_path_topic_id
    @openapi.apply_signature
    def get(self, request, topic_id, fields=_s_fields_func(_TopicModel)):
        topic = _get_topic(topic_id)
        return JsonResponse(qd.struct(_TopicModel, fields).parse(topic))

    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(schema=_put_topic_struct),
        },
    )
    @openapi.response(
        200, content={"application/json": openapi.MediaType(schema=_topic_struct)}
    )
    @required_jwt
    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    def put(self, request, topic_id, body: dict):
        topic = _get_topic(topic_id)
        for k, v in body.items():
            setattr(topic, k, v)
        topic.save()
        return JsonResponse(_topic_struct.parse(topic))

    @openapi.response(204)
    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    @required_jwt
    def delete(self, request, topic_id):
        topic = _get_topic(topic_id)
        topic.delete()
        return HttpResponse(status=204)


class _ClientTopicModel(qd.Model):
    id = qd.Field(z.field(z.int(), meta={"readonly": True}))
    title = qd.Field(z.str())
    topic = _TopicModel()
    client = _ClientModel()
    published_at = qd.Field(z.field(z.datetime(), meta={"readonly": True}))


class _ClientTopicListModel(_PaginationModel):
    results = _ClientTopicModel(many=True)


def _check_topic_id(topic_id):
    if not models.Topic.objects.filter(pk=topic_id).exists():
        raise Http404


class DashTopicClientTopicListAPI(APIView):
    @_path_topic_id
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(
                schema=qd.struct(_ClientTopicListModel, optional=True)
            )
        },
    )
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        topic_id,
        fields=_s_fields_func(_ClientTopicListModel),
        page=_s_page,
        page_size=_s_page_size,
    ):
        _check_topic_id(topic_id)
        rv = _process_clienttopic_list(
            models.ClientTopic.objects.filter(topic_id=topic_id),
            fields,
            page,
            page_size,
        )
        return JsonResponse(qd.struct(_ClientTopicListModel, fields).parse(rv))


def _process_clienttopic_list(
    queryset, fields: qd.FieldSelector, page: int, page_size: int
):
    rv = {}
    if fields.contains(_ClientTopicListModel.results.topic):
        queryset = queryset.select_related("topic")
    if fields.contains(_ClientTopicListModel.results.client):
        queryset = queryset.select_related("client")
    if fields.contains(_ClientTopicListModel.results):
        rv["results"] = _paginate(
            queryset.only(*fields.keys(_ClientTopicListModel.results)),
            page,
            page_size,
        )
    if fields.contains(_ClientTopicListModel.page):
        rv.update(page=page)
    if fields.contains(_ClientTopicListModel.page_size):
        rv.update(page_size=page_size)
    if fields.contains(_ClientTopicListModel.total):
        rv.update(total=queryset.count())
    return rv


class DashTopicWallaperListAPI(APIView):
    __get_response_schema = z.struct(
        {
            "results": z.to.list(_dash_wallpaper_struct),
            **_paginator_fields,
        }
    )

    @_path_topic_id
    @openapi.declare(tags=["dashboard"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.apply_signature
    def get(
        self,
        request,
        topic_id,
        page=_s_page,
        page_size=_s_page_size,
    ):
        _check_topic_id(topic_id)
        queryset = (
            models.TopicWallpaper.objects.filter(topic_id=topic_id)
            .select_related("wallpaper")
            .order_by("-pk")
        )

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": (
                        x.wallpaper for x in _paginate(queryset, page, page_size)
                    ),
                    "page": page,
                    "page_size": page_size,
                    "total": queryset.count(),
                }
            )
        )

    @openapi.declare(tags=["dashboard"])
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(
                schema=z.struct(
                    {
                        "wallpaper_id": z.int(),
                    }
                )
            )
        },
    )
    @openapi.response(201)
    @openapi.response(409)
    @openapi.apply_signature
    def post(self, request, body, topic_id=_s_path_topic_id):
        _check_topic_id(topic_id)
        wallpaper_id = body["wallpaper_id"]
        wallpaper = _get_wallpaper(wallpaper_id)
        topic = _get_topic(topic_id)
        _, created = models.TopicWallpaper.objects.get_or_create(
            topic=topic, wallpaper=wallpaper
        )
        if created:
            return HttpResponse(status=201)
        return HttpResponse(status=409)


_s_path_wallpaper_id = openapi.s_path(name="wallpaper_id", schema=z.to.int())


def _get_wallpaper(pk):
    try:
        return models.WallpaperImage.objects.get(pk=pk)
    except models.WallpaperImage.DoesNotExist:
        raise Http404


class DashTopicWallaperDetailAPI(APIView):
    @openapi.response(204)
    @openapi.declare(tags=["dashboard"], summary="将壁纸与话题关联")
    @required_jwt
    @openapi.apply_signature
    def put(
        self, request, topic_id=_s_path_topic_id, wallpaper_id=_s_path_wallpaper_id
    ):
        topic = _get_topic(topic_id)
        wallpaper = _get_wallpaper(wallpaper_id)
        models.TopicWallpaper.objects.get_or_create(topic=topic, wallpaper=wallpaper)
        return HttpResponse(status=204)

    @required_jwt
    @openapi.response(204)
    @openapi.declare(tags=["dashboard"], summary="取消壁纸与话题的关联")
    @openapi.apply_signature
    def delete(
        self, request, topic_id=_s_path_topic_id, wallpaper_id=_s_path_wallpaper_id
    ):
        topic = _get_topic(topic_id)
        wallpaper = _get_wallpaper(wallpaper_id)
        models.TopicWallpaper.objects.filter(topic=topic, wallpaper=wallpaper).delete()
        return HttpResponse(status=204)


class DashSearchWallpapersAPI(APIView):
    __get_response_schema = z.struct(
        {
            "q": z.str(),
            "results": z.to.list(
                z.struct(
                    {
                        "id": z.int(),
                        "wallpaper": _wallpaper_with_id_struct,
                    }
                )
            ),
        }
    )

    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        query=openapi.s_query(
            schema=z.str().strip().min(1),
            name="q",
        ),
        after=openapi.s_query(
            schema=z.to.int()
            .transform(lambda x: models.CLIPTag.objects.filter(pk=x).first())
            .ensure(lambda x: x is not None),
            py_default=None,
        ),
        limit=openapi.s_query(schema=z.to.int().gte(1).lte(1000), py_default=20),
        sizes=s_query_image_sizes,
        exclude_topic_id=openapi.s_query(
            schema=z.to.int().lte(_SQL_MAX_INT).gte(_SQL_MIN_INT),
            py_default=None,
            description="排除指定专题下的壁纸",
        ),
    ):
        queryset = (
            models.CLIPTag.qualified.filter(name__startswith=query)
            .select_related("wallpaper")
            .order_by("-score", "-pk")
        )
        if after is not None:
            queryset = queryset.filter(
                Q(score__lt=after.score) | Q(score=after.score, pk__lt=after.score)
            )

        if exclude_topic_id is not None:
            queryset = queryset.exclude(
                wallpaper__topicwallpaper__topic_id=exclude_topic_id
            )

        queryset = queryset[:limit]

        if sizes:
            for item in queryset:
                item.wallpaper.set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "q": query,
                    "results": queryset,
                }
            )
        )


class DashSearchWallpapersAPIV2(APIView):
    __get_response_schema = z.struct(
        {
            "q": z.str(),
            "wallpapers": z.to.list(
                z.struct(
                    {
                        **_wallpaper_with_id_struct.fields,
                        "search_score": z.float(
                            meta={"oas": {"description": "搜索得分"}}
                        ),
                    }
                ),
            ),
        }
    )

    @required_jwt
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    def get(
        self,
        request,
        query=openapi.s_query(
            schema=z.str().strip().min(1),
            name="q",
        ),
        sized_for=_s_sized_for,
    ):
        from clip_service import api as clip_service_api

        from ..services import qdrant

        text_vector = clip_service_api.encode_text_encode_text_post(query).vector
        results = qdrant.search_wallpapers(text_vector, sized_for=sized_for)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "q": query,
                    "wallpapers": (
                        ChainObject(item[0], {"search_score": item[1]})
                        for item in results
                    ),
                }
            )
        )


class DashTopicSuggestedTagsAPI(APIView):
    __get_response_schema = z.struct(
        {
            "tags": z.to.list(z.str()),
        }
    )

    @_path_topic_id
    @openapi.declare(tags=["dashboard"], summary="获取 Topic 可能的标签")
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @required_jwt
    def get(self, request, topic_id):
        topic = _get_topic(topic_id)
        random_topic_wallpapers = (
            models.TopicWallpaper.objects.filter(topic=topic)
            .select_related("wallpaper")
            .order_by("?")[:10]
        )
        tags = (
            models.CLIPTag.qualified.filter(
                wallpaper__in=(item.wallpaper for item in random_topic_wallpapers)
            )
            .values_list("name", flat=True)
            .order_by("-score")[:20]
        )
        return JsonResponse(self.__get_response_schema.parse({"tags": set(tags)}))


def _get_client(client_id):
    try:
        return models.Client.objects.get(pk=client_id)
    except models.Client.DoesNotExist:
        raise Http404


class DashPublishTopicAPI(APIView):
    post_body_struct = z.struct(
        {
            "title": z.str().strip().min(1),
            # "group": z.field(z.str().strip().min(1)).optional(), # 先不使用 group 看看效果
        }
    )

    @_path_topic_id
    @openapi.path("client_id", schema=z.str())
    @required_jwt
    @openapi.response(201)
    @openapi.declare(tags=["dashboard"], summary="发布专题")
    @openapi.body(
        "body",
        content={"application/json": openapi.MediaType(schema=post_body_struct)},
    )
    @openapi.response(409)
    def post(self, request, topic_id, client_id, body: dict):
        topic = _get_topic(topic_id)
        client = _get_client(client_id)
        obj, created = models.ClientTopic.objects.get_or_create(
            topic=topic,
            client=client,
            defaults={"title": body["title"]},
        )
        if not created:
            return HttpResponse(status=409)
        return HttpResponse(status=201)


class DashClientTopicListAPI(APIView):
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(
                schema=qd.struct(_ClientTopicListModel, optional=True)
            )
        },
    )
    @openapi.apply_signature
    @openapi.declare(tags=["dashboard"])
    def get(
        self,
        request,
        page=_s_page,
        page_size=_s_page_size,
        fields=_s_fields_func(_ClientTopicListModel),
        client_id=openapi.s_query(
            schema=z.str().transform(lambda x: uuid.UUID(x, version=4)),
            py_default=None,
            description="[filter]",
        ),
    ):
        queryset = models.ClientTopic.objects.order_by("-pk")
        if client_id is not None:
            queryset = queryset.filter(client_id=client_id)
        rv = _process_clienttopic_list(queryset, fields, page, page_size)
        return JsonResponse(qd.struct(_ClientTopicListModel, fields).parse(rv))


class DashClientTopicDetailAPI(APIView):
    __path_clienttopic_id = openapi.path("clienttopic_id", schema=z.to.int())
    __get_response_schema = qd.struct(_ClientTopicModel, usefor="readself")

    @__path_clienttopic_id
    @required_jwt
    @openapi.declare(tags=["dashboard"])
    @openapi.body(
        "body",
        content={
            "application/json": openapi.MediaType(
                schema=qd.struct(_ClientTopicModel, usefor="write")
            )
        },
    )
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    def put(self, request, clienttopic_id, body):
        try:
            obj = models.ClientTopic.objects.get(pk=clienttopic_id)
        except models.ClientTopic.DoesNotExist:
            raise Http404
        for k, v in body.items():
            setattr(obj, k, v)
        obj.save()
        return JsonResponse(self.__get_response_schema.parse(obj))

    @required_jwt
    @openapi.declare(tags=["dashboard"])
    @__path_clienttopic_id
    @openapi.response(204)
    def delete(self, request, clienttopic_id):
        models.ClientTopic.objects.filter(pk=clienttopic_id).delete()
        return HttpResponse(status=204)


class DashClientListAPI(APIView):
    @openapi.response(
        200,
        content={
            "application/json": openapi.MediaType(
                schema=qd.struct(_ClientListModel, optional=True)
            )
        },
    )
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.declare(tags=["dashboard"])
    @openapi.apply_signature
    def get(
        self,
        request,
        fields=_s_fields_func(_ClientListModel),
        page=_s_page,
        page_size=_s_page_size,
    ):
        queryset = models.Client.objects.all().order_by("-pk")
        rv = {}
        if fields.contains(_ClientListModel.results):
            rv["results"] = queryset.only(*fields.keys(_ClientListModel.results))
        if fields.contains(_ClientListModel.page):
            rv.update(page=page)
        if fields.contains(_ClientListModel.page_size):
            rv.update(page_size=page_size)
        if fields.contains(_ClientListModel.total):
            rv.update(total=queryset.count())
        return JsonResponse(qd.struct(_ClientListModel, fields).parse(rv))


class DashUserInfoAPI(APIView):
    __get_response_schema = z.struct(
        {
            "email": z.str(),
            "is_superuser": z.bool(
                meta={
                    "oas": {
                        "description": "是否为超级管理员 (如果是超管，则其默认拥有所有权限，不受 permissions 约束)"
                    }
                }
            ),
            "permissions": z.list(
                z.str(),
                meta={
                    "oas": {
                        "description": "用户拥有的接口权限列表，为 operationId 集合。通过对照接口 operationId 判断是否有权限。"
                    }
                },
            ),
        }
    )

    @required_jwt(userkey="user")
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["dashboard"])
    def get(self, request, user: User):
        user.permissions = list(user_permissions(user))  # type: ignore
        return JsonResponse(self.__get_response_schema.parse(user))


class DashWallpaperListAPI(APIView):
    __get_response_schema = z.struct(
        {"results": z.to.list(_dash_wallpaper_struct), **_paginator_fields}
    )

    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.declare(tags=["dashboard"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        page=_s_page,
        page_size=_s_page_size,
        sized_for=_s_sized_for,
        in_topic=s_in_topic,
    ):
        queryset = models.WallpaperImage.objects.order_by("-pk")

        if sized_for is not None:
            queryset = _apply_sized_for(queryset, sized_for)

        if in_topic is not None:
            if in_topic:
                queryset = queryset.filter(topicwallpaper__isnull=False)
            else:
                queryset = queryset.filter(topicwallpaper__isnull=True)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": _paginate(queryset, page, page_size),
                    "page": page,
                    "page_size": page_size,
                    "total": queryset.count(),
                }
            )
        )


class DashWallpaperTopicListAPI(APIView):
    __get_response_schema = z.struct({"topics": z.to.list(_topic_struct)})

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["dashboard"])
    @required_jwt
    @openapi.apply_signature
    def get(
        self,
        request,
        wallpaper_id=_s_path_wallpaper_id,
    ):
        wallpaper = _get_wallpaper(wallpaper_id)

        topics = models.Topic.objects.filter(
            pk__in=models.TopicWallpaper.objects.filter(
                wallpaper=wallpaper
            ).values_list("topic_id", flat=True)
        )

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "topics": topics,
                }
            )
        )


class DashStatsAPI(APIView):
    __get_response_schema = z.struct(
        {
            "wallpaper_count": z.int().gte(0),
            "wallpaper_in_topic_count": z.int().gte(0),
            "topic_count": z.int().gte(0),
            "topic_in_client_count": z.int().gte(0),
        }
    )

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["dashboard"])
    @_conditional_decorator(not settings.DEBUG, required_jwt)
    def get(self, request):
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "wallpaper_count": models.WallpaperImage.objects.count(),
                    "wallpaper_in_topic_count": models.TopicWallpaper.objects.values_list(
                        "wallpaper_id", flat=True
                    )
                    .distinct()
                    .count(),
                    "topic_count": models.Topic.objects.count(),
                    "topic_in_client_count": models.ClientTopic.objects.values_list(
                        "topic_id", flat=True
                    )
                    .distinct()
                    .count(),
                }
            )
        )


class DashWallpaperSimilarAPI(APIView):
    __get_response_schema = z.struct(
        {
            "results": z.to.list(
                z.struct(
                    {
                        "wallpaper": _dash_wallpaper_struct,
                        "score": z.float(),
                    }
                )
            )
        }
    )

    @_conditional_decorator(not settings.DEBUG, required_jwt)
    @openapi.declare(tags=["dashboard"], deprecated=True)
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(self, request, wallpaper_id=_s_path_wallpaper_id):
        from wallpapers.services import qdrant

        wallpaper = _get_wallpaper(wallpaper_id)
        results, has_next = qdrant.get_similar_wallpapers(wallpaper)
        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": [
                        {"wallpaper": wp, "score": score} for wp, score in results
                    ],
                }
            )
        )
