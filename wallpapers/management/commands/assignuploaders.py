from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "为壁纸随机分配上传者"

    def handle(self, *args, **options):
        from wallpapers.models import UploaderWallpaper, WallpaperImage
        from wallpapers.services.misc import random_uploader

        for wallpaper in WallpaperImage.objects.filter(
            uploaderwallpaper__isnull=True
        ).iterator():
            uploader = random_uploader()
            if not uploader:
                raise RuntimeError("没有可用的上传者")

            self.stdout.write(f"为壁纸 {wallpaper.url} 分配上传者 {uploader.name}")

            UploaderWallpaper.objects.create(
                uploader=uploader,
                wallpaper=wallpaper,
            )
