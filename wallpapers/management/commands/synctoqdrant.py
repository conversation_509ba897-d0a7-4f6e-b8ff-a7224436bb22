import logging

from django.core.management.base import BaseCommand

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        from project.log import ConsoleFilter
        from wallpapers.models import WallpaperImage
        from wallpapers.services.qdrant import sync_wallpaper

        ConsoleFilter.setLevel(logging.INFO)

        for wallpaper in WallpaperImage.objects.iterator():
            try:
                sync_wallpaper(wallpaper)
            except Exception:
                logger.exception("同步壁纸到 Qdrant 失败 wallpaper=%r", wallpaper)
