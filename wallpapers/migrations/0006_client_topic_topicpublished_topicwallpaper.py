# Generated by Django 5.2.3 on 2025-07-17 01:51

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("wallpapers", "0005_alter_cliptag_managers_alter_cliptag_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        verbose_name="客户端ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, verbose_name="客户端名称")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "客户端",
                "verbose_name_plural": "客户端",
            },
        ),
        migrations.CreateModel(
            name="Topic",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "comment",
                    models.TextField(blank=True, null=True, verbose_name="备注"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "专题",
                "verbose_name_plural": "专题",
            },
        ),
        migrations.CreateModel(
            name="TopicPublished",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100, verbose_name="标题")),
                (
                    "group",
                    models.CharField(
                        default="default",
                        help_text="分组可用于表示将专题发布到不同的位置，如：专栏、特辑等等。这是一个抽象的表示，具体如何使用由客户端决定。",
                        max_length=50,
                        verbose_name="分组",
                    ),
                ),
                (
                    "published_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="发布时间"),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.client",
                        verbose_name="客户端",
                    ),
                ),
                (
                    "topic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.topic",
                        verbose_name="专题",
                    ),
                ),
            ],
            options={
                "verbose_name": "发布的专题",
                "verbose_name_plural": "发布的专题",
                "unique_together": {("topic", "client", "group")},
            },
        ),
        migrations.CreateModel(
            name="TopicWallpaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "topic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.topic",
                        verbose_name="专题",
                    ),
                ),
                (
                    "wallpaper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.wallpaperimage",
                        verbose_name="壁纸",
                    ),
                ),
            ],
            options={
                "verbose_name": "专题壁纸",
                "verbose_name_plural": "专题壁纸",
                "unique_together": {("topic", "wallpaper")},
            },
        ),
    ]
