# Generated by Django 5.2.3 on 2025-09-26 09:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("wallpapers", "0007_rename_topicpublished_clienttopic_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="KMeansColorCluster",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("r", models.SmallIntegerField(verbose_name="R")),
                ("g", models.SmallIntegerField(verbose_name="G")),
                ("b", models.SmallIntegerField(verbose_name="B")),
                ("proportion", models.FloatField(verbose_name="占比")),
                (
                    "wallpaper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="wallpapers.wallpaperimage",
                        verbose_name="壁纸",
                    ),
                ),
            ],
            options={
                "verbose_name": "K-means 聚类颜色",
                "verbose_name_plural": "K-means 聚类颜色",
                "unique_together": {("wallpaper", "r", "g", "b")},
            },
        ),
    ]
