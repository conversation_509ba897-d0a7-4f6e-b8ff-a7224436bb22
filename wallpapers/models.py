from __future__ import annotations

import typing
import uuid

from django.db import models

from wallpapers.utils.colors import ColorFamily
from wallpapers.utils.imgproxy import get_imgproxy_url


class WallpaperImage(models.Model):
    url = models.URLField(verbose_name="图片地址")
    format = models.CharField(max_length=10, verbose_name="格式")
    width = models.IntegerField(verbose_name="宽度")
    height = models.IntegerField(verbose_name="高度")
    aspect_ratio = models.FloatField(verbose_name="宽高比")
    pixels = models.IntegerField(verbose_name="像素数")
    filesize = models.IntegerField(verbose_name="文件大小", help_text="单位为字节")
    content_md5 = models.CharField(max_length=32, verbose_name="内容MD5", unique=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "壁纸图片"
        verbose_name_plural = "壁纸图片"

    cliptag_set: models.QuerySet[CLIPTag]
    uploaderwallpaper_set: models.QuerySet[UploaderWallpaper]
    uploader: Uploader  # earliest uploader

    _images: dict[str, str] | None = None

    @property
    def images(self):
        if self._images is None:
            return {"default": get_imgproxy_url(self.url, width=500, height=500)}
        return self._images

    def set_images(self, sizes: typing.Iterable[tuple[int, int]]):
        images = {}
        for size in sizes:
            width, height = size
            images[f"{width}x{height}"] = get_imgproxy_url(
                self.url,
                width=width,
                height=height,
            )
        self._images = images


class Uploader(models.Model):
    name = models.CharField(max_length=100, verbose_name="用户名")
    email = models.EmailField(verbose_name="邮箱", null=True, blank=True, unique=True)
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name="注册时间")

    class Meta:
        verbose_name = "壁纸上传者"
        verbose_name_plural = "壁纸上传者"

    def __str__(self) -> str:
        return self.name


class UploaderWallpaper(models.Model):
    uploader = models.ForeignKey(
        Uploader, on_delete=models.CASCADE, verbose_name="上传者"
    )
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    wallpaper_id: int
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "上传者壁纸"
        verbose_name_plural = "上传者壁纸"
        unique_together = [("uploader", "wallpaper")]


class ImageColorFamily(models.Model):
    wallpaper = models.OneToOneField(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    color_family = models.CharField(
        max_length=10,
        verbose_name="色系",
        choices=ColorFamily.choices(),
    )

    class Meta:
        verbose_name = "壁纸色系"
        verbose_name_plural = "壁纸色系"


TAG_SCORE_THRESHOLD = 0.4  # 标签分数阈值，低于这个值将不准确


class CLIPTag(models.Model):
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    name = models.CharField(max_length=50, verbose_name="标签名称")
    score = models.FloatField(verbose_name="分数")

    # TODO: 改名
    # TODO: 增长 lang 字段，枚举类型，用于区分不同语言的标签
    # TODO: 增加 origin 字段，枚举类型，用于区分不同来源的标签（如手动填写， CLIP-v2 等的呢）

    class Meta:
        verbose_name = "CLIP 分类标签"
        verbose_name_plural = "CLIP 分类标签"
        unique_together = [("wallpaper", "name")]

    class _QualifiedMangar(models.Manager):
        def get_queryset(self):
            return super().get_queryset().filter(score__gte=TAG_SCORE_THRESHOLD)

    qualified: models.Manager[CLIPTag] = _QualifiedMangar()  # default mangar
    objects = models.Manager()


class Client(models.Model):
    id = models.UUIDField(
        primary_key=True, default=uuid.uuid4, editable=False, verbose_name="客户端ID"
    )
    name = models.CharField(max_length=50, verbose_name="客户端名称")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "客户端"
        verbose_name_plural = "客户端"


class Topic(models.Model):
    comment = models.TextField(verbose_name="备注", null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "专题"
        verbose_name_plural = "专题"


class TopicWallpaper(models.Model):
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, verbose_name="专题")
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "专题壁纸"
        verbose_name_plural = "专题壁纸"
        unique_together = [("topic", "wallpaper")]


class ClientTopic(models.Model):
    title = models.CharField(max_length=100, verbose_name="标题")
    topic_id: int
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, verbose_name="专题")
    client = models.ForeignKey(Client, on_delete=models.CASCADE, verbose_name="客户端")
    group = models.CharField(
        max_length=50,
        default="default",
        verbose_name="分组",
        help_text="分组可用于表示将专题发布到不同的位置，如：专栏、特辑等等。这是一个抽象的表示，具体如何使用由客户端决定。",
    )
    published_at = models.DateTimeField(auto_now_add=True, verbose_name="发布时间")

    class Meta:
        verbose_name = "客户端专题"
        verbose_name_plural = "客户端专题"
        unique_together = [("topic", "client", "group")]


class KMeansColorCluster(models.Model):
    wallpaper = models.ForeignKey(
        WallpaperImage, on_delete=models.CASCADE, verbose_name="壁纸"
    )
    r = models.SmallIntegerField(verbose_name="R")
    g = models.SmallIntegerField(verbose_name="G")
    b = models.SmallIntegerField(verbose_name="B")
    proportion = models.FloatField(verbose_name="占比")

    class Meta:
        verbose_name = "K-means 聚类颜色"
        verbose_name_plural = "K-means 聚类颜色"
        unique_together = [("wallpaper", "r", "g", "b")]
