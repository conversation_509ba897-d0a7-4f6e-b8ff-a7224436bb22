import io
import logging
from dataclasses import dataclass

import cv2
import numpy as np
from PIL import Image
from sklearn.cluster import KMeans

from wallpapers.models import KMeansColorCluster, WallpaperImage
from wallpapers.services.misc import download_url

_NUM_CLUSTERS = 5  # 每张图片提取主色数量

logger = logging.getLogger(__name__)


@dataclass(kw_only=True)
class ColorCluster:
    r: int
    g: int
    b: int
    proportion: float


def extract_dominant_colors(image: Image.Image) -> list[ColorCluster]:
    img = np.array(image.convert("RGB"))

    # 缩小图片，提高 K-means 速度
    img_small = cv2.resize(img, (64, 64), interpolation=cv2.INTER_AREA)
    pixels = img_small.reshape(-1, 3)

    # K-means 聚类
    kmeans = KMeans(n_clusters=_NUM_CLUSTERS, random_state=1)
    labels = kmeans.fit_predict(pixels)
    centers = kmeans.cluster_centers_  # RGB 颜色
    proportions = np.bincount(labels) / len(labels)  # 占比

    rv: list[ColorCluster] = []
    for center, p in zip(centers, proportions):
        center = center.astype(np.int32)
        rv.append(ColorCluster(r=center[0], g=center[1], b=center[2], proportion=p))
    return rv


def sync_colors():
    for wallpaper in WallpaperImage.objects.filter(
        kmeanscolorcluster__isnull=True
    ).iterator():
        logger.info("正在处理壁纸 %s", wallpaper.url)

        content = download_url(wallpaper.url)
        if content is None:
            logger.warning("下载壁纸失败 %s", wallpaper.url)
            continue

        image = Image.open(io.BytesIO(content))

        # 批量添加
        KMeansColorCluster.objects.bulk_create(
            [
                KMeansColorCluster(
                    wallpaper=wallpaper,
                    r=color.r,
                    g=color.g,
                    b=color.b,
                    proportion=color.proportion,
                )
                for color in extract_dominant_colors(image)
            ],
            ignore_conflicts=True,
        )
