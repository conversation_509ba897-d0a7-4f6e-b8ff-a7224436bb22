import logging

import httpx

from wallpapers import models

logger = logging.getLogger(__name__)


def download_url(url: str) -> bytes | None:
    try:
        response = httpx.get(url)
        response.raise_for_status()
        return response.content
    except httpx.HTTPError:
        logger.exception("下载失败: %s", url)
        return None


def get_top_tags(n):
    from django.db.models import Count

    return (
        models.CLIPTag.qualified.values("name")
        .annotate(count=Count("id"))
        .order_by("-count")[:n]
    )


def random_uploader() -> models.Uploader | None:
    """随机获取一个上传者"""
    return models.Uploader.objects.filter(email=None).order_by("?").first()
