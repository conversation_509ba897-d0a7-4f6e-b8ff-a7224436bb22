import logging
import typing
import uuid
from typing import OrderedDict, Sequence

import configat
from django.utils.functional import Simple<PERSON>azyObject
from qdrant_client import QdrantClient
from qdrant_client import models as qm

from wallpapers import models
from wallpapers.common import SizedForEnum
from wallpapers.utils import numfilter

logger = logging.getLogger(__name__)

QDRANT_URL = configat.resolve("@env:QDRANT_URL")
QDRANT_API_KEY = configat.resolve("@env:QDRANT_API_KEY", default=None)
COLLECTION_NAME = "CLIP_ViT-L-14"

client = typing.cast(
    QdrantClient,
    SimpleLazyObject(lambda: QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)),
)


def _build_payload(wallpaper: models.WallpaperImage) -> dict:
    return {
        "aspect_ratio": wallpaper.aspect_ratio,
    }


def search_wallpapers(
    vector: list[float], *, sized_for: SizedForEnum | None = None
) -> Sequence[tuple[models.WallpaperImage, float]]:
    """通过向量查询最接近的壁纸"""
    query_filter = None
    if sized_for is not None:
        if sized_for == SizedForEnum.DESKTOP:
            query_filter = qm.Filter(
                must=qm.FieldCondition(key="aspect_ratio", range=qm.Range(gte=1.2))
            )
        elif sized_for == SizedForEnum.MOBILE:
            query_filter = qm.Filter(
                must=qm.FieldCondition(key="aspect_ratio", range=qm.Range(lte=1.2))
            )

    points = client.search(
        COLLECTION_NAME,
        query_vector=vector,
        query_filter=query_filter,
        limit=30,
    )
    md5_to_point_dict = OrderedDict(
        (uuid.UUID(p.id).hex, p) for p in points if isinstance(p.id, str)
    )
    wallpapers = models.WallpaperImage.objects.filter(
        content_md5__in=md5_to_point_dict.keys()
    )

    wallpaper_dict = {wp.content_md5: wp for wp in wallpapers}

    rv: Sequence[tuple[models.WallpaperImage, float]] = []
    # 按照搜索结果的顺序排序
    for md5 in md5_to_point_dict:
        if md5 in wallpaper_dict:
            rv.append((wallpaper_dict[md5], md5_to_point_dict[md5].score))
    return rv


def upsert_wallpaper_vector(wallpaper: models.WallpaperImage, vector: list[float]):
    logger.debug("Qdrant 插入 wallpaper=%s", wallpaper)
    client.upsert(
        COLLECTION_NAME,
        points=[
            qm.PointStruct(
                id=wallpaper.content_md5,
                vector=vector,
                payload=_build_payload(wallpaper),
            )
        ],
    )


def delete_data_by_id(id: str):
    logger.debug("Qdrant 删除 id=%s", id)
    client.delete(COLLECTION_NAME, points_selector=[id])


def sync_wallpaper(wallpaper: models.WallpaperImage):
    """同步壁纸数据到 Qdrant"""
    from clip_service import api as clip_service_api
    from wallpapers.services.misc import download_url

    logger.info("正在同步壁纸到 Qdrant wallpaper=%r", wallpaper)

    # 先判断 qdrant 中是否已经存在
    records = client.retrieve(COLLECTION_NAME, [wallpaper.content_md5])
    if records:
        logger.info("壁纸已经存在 Qdrant 中, 更新 payload。 wallpaper=%r", wallpaper)
        # 更新 payload
        client.overwrite_payload(
            COLLECTION_NAME,
            payload=_build_payload(wallpaper),
            points=[wallpaper.content_md5],
        )
        return

    image_content = download_url(wallpaper.url)
    if image_content is None:
        return
    vector = clip_service_api.encode_image_encode_image_post(image_content).vector
    upsert_wallpaper_vector(wallpaper, vector)
    logger.info("壁纸同步到 Qdrant 成功 wallpaper=%r", wallpaper)


def _build_aspect_ratio_filter(
    constraints: list[list[numfilter.Constraint]],
) -> qm.Filter:
    # 构建 Qdrant 过滤条件：每组之间是 OR 关系，组内是 AND 关系
    should_conditions = []

    for constraint_group in constraints:
        # 每个组内的约束是 AND 关系
        must_conditions = []

        for constraint in constraint_group:
            if constraint.operator == numfilter.Operator.EQ:
                # 对于浮点数相等比较，使用一个很小的范围来模拟
                epsilon = 1e-6
                must_conditions.append(
                    qm.FieldCondition(
                        key="aspect_ratio",
                        range=qm.Range(
                            gte=constraint.value - epsilon,
                            lte=constraint.value + epsilon,
                        ),
                    )
                )
            elif constraint.operator == numfilter.Operator.GTE:
                must_conditions.append(
                    qm.FieldCondition(
                        key="aspect_ratio", range=qm.Range(gte=constraint.value)
                    )
                )
            elif constraint.operator == numfilter.Operator.LTE:
                must_conditions.append(
                    qm.FieldCondition(
                        key="aspect_ratio", range=qm.Range(lte=constraint.value)
                    )
                )
            elif constraint.operator == numfilter.Operator.GT:
                must_conditions.append(
                    qm.FieldCondition(
                        key="aspect_ratio", range=qm.Range(gt=constraint.value)
                    )
                )
            elif constraint.operator == numfilter.Operator.LT:
                must_conditions.append(
                    qm.FieldCondition(
                        key="aspect_ratio", range=qm.Range(lt=constraint.value)
                    )
                )

        if must_conditions:
            should_conditions.append(qm.Filter(must=must_conditions))

    return qm.Filter(should=should_conditions)


def get_similar_wallpapers(
    wallpaper: models.WallpaperImage,
    /,
    limit: int = 30,
    offset: int | None = None,
    score_threshold: float | None = None,
    aspect_ratio: list[list[numfilter.Constraint]] | None = None,
) -> tuple[Sequence[tuple[models.WallpaperImage, float]], bool]:
    """获取相似的壁纸"""
    nearest = client.query_points(
        COLLECTION_NAME,
        query=wallpaper.content_md5,
        limit=limit + 1,  # 多取一个，用于判断是否有下一页
        offset=offset,
        score_threshold=score_threshold,
        query_filter=None
        if aspect_ratio is None
        else _build_aspect_ratio_filter(aspect_ratio),
    ).points
    has_next = len(nearest) > limit
    nearest = nearest[:limit]

    md5_to_point_dict = dict(
        (uuid.UUID(p.id).hex, p) for p in nearest if isinstance(p.id, str)
    )
    wallpapers = models.WallpaperImage.objects.filter(
        content_md5__in=md5_to_point_dict.keys()
    )
    wallpaper_dict = {wp.content_md5: wp for wp in wallpapers}

    rv: Sequence[tuple[models.WallpaperImage, float]] = [
        (wallpaper_dict[md5], md5_to_point_dict[md5].score)
        for md5 in md5_to_point_dict
        if md5 in wallpaper_dict
    ]
    # 按照 score 从大到小排序
    rv.sort(key=lambda x: x[1], reverse=True)
    return rv, has_next
