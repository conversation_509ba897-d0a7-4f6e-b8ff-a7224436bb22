import logging

from django.db.models.signals import post_delete
from django.dispatch import receiver

from .models import WallpaperImage

logger = logging.getLogger(__name__)


@receiver(post_delete, sender=WallpaperImage)
def delete_wallpaper_from_qdrant_on_wallpaper_deleted(
    sender, instance: WallpaperImage, **kwargs
):
    from .tasks import delete_wallpaper_from_qdrant

    logger.debug("壁纸删除信号触发 instance=%s", instance)

    delete_wallpaper_from_qdrant.delay(instance.content_md5)
