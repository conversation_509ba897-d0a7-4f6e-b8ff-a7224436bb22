import io
import logging
from hashlib import md5

import PIL.Image
from celery import shared_task
from django.db import transaction

from . import models

logger = logging.getLogger(__name__)


@shared_task
def delete_wallpaper_from_qdrant(content_md5):
    """直接使用 content_md5 从 Qdrant 删除向量"""
    from wallpapers.services.qdrant import delete_data_by_id

    delete_data_by_id(id=content_md5)


@shared_task
def create_wallpaper(url: str):
    from clip_service import api as clip_service_api
    from wallpapers.services import qdrant
    from wallpapers.services.misc import download_url, random_uploader

    content = download_url(url)
    if content is None:
        return

    image = PIL.Image.open(io.BytesIO(content))
    assert image.format is not None

    width, height = image.size
    if width * height < 720 * 1280:  # 最小壁纸要求尺寸
        logger.warning(f"壁纸尺寸过小: {width}x{height}")
        return

    content_md5 = md5(content).hexdigest()

    with transaction.atomic():
        obj, created = models.WallpaperImage.objects.get_or_create(
            content_md5=content_md5,
            defaults={
                "url": url,
                "format": image.format.lower(),
                "width": width,
                "height": height,
                "aspect_ratio": width / height,
                "pixels": width * height,
                "filesize": len(content),
            },
        )
        logger.info("新增壁纸 obj=%r created=%s", obj, created)

        # 如果为新增，则创建其他数据
        # 将以下服务调用一并放在事务中，保证数据一致性
        if created:
            # 随机分配上传者
            models.UploaderWallpaper.objects.create(
                uploader=random_uploader(), wallpaper=obj
            )

            # 调用 CLIP 服务进行向量化
            vector = clip_service_api.encode_image_encode_image_post(content).vector
            # 插入 Qdrant
            qdrant.upsert_wallpaper_vector(obj, vector)
