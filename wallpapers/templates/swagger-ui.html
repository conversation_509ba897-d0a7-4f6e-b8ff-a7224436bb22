<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="SwaggerUI" />
    <title>SwaggerUI</title>
    <link
      rel="stylesheet"
      href="https://unpkg.com/swagger-ui-dist@5.24.1/swagger-ui.css"
    />
  </head>
  <body>
    <div id="swagger-ui"></div>
    <script
      src="https://unpkg.com/swagger-ui-dist@5.24.1/swagger-ui-bundle.js"
      crossorigin
    ></script>
    {{ config|json_script:"config" }}
    <script>
      var config =
        JSON.parse(document.getElementById("config").textContent) || {};
      Object.assign(config, {
        dom_id: "#swagger-ui",
        deepLinking: true,
        defaultModelRendering: "model",
        defaultModelExpandDepth: 10,
      });

      window.onload = () => {
        window.ui = SwaggerUIBundle(config);
      };
    </script>
  </body>
</html>
