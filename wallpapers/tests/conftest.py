from unittest import mock

import pytest


@pytest.fixture(autouse=True, scope="session")
def mock_vendors():
    """全局 mock 第三方服务依赖"""
    with (
        mock.patch("wallpapers.services.qdrant.client", new=mock.MagicMock()),
        mock.patch("clip_service_client.DefaultApi", new=mock.MagicMock()),
    ):
        yield


@pytest.fixture(autouse=True, scope="session")
def mock_reqests():
    with mock.patch("requests.api.request", new=mock.MagicMock()):
        yield
