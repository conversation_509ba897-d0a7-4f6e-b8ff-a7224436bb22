import random
from unittest import mock

import pytest
import schemathesis
from django.contrib.auth import get_user_model
from django.urls import reverse
from schemathesis.generation.case import Case

from project.wsgi import application
from wallpapers.auth import generate_access_token

User = get_user_model()

schema = schemathesis.openapi.from_wsgi(
    reverse("wallpapers-api:openapi"),
    application,
)


@pytest.fixture(autouse=True)
def mock_cloudflare_tunstile():
    def random_bool(_):
        return random.choice([True, False])

    async def async_random_bool(_):
        return random_bool(_)

    with (
        mock.patch("wallpapers.utils.cloudflare._verify_sync", new=random_bool),
        mock.patch("wallpapers.utils.cloudflare._verify_async", new=async_random_bool),
    ):
        yield


@schema.parametrize()
@pytest.mark.django_db(transaction=True)
def test_api(case: Case):
    if case.media_type == "application/x-www-form-urlencoded" and isinstance(
        case.body, list
    ):
        # BUG: 这种生成的 case，不会被正确构建
        return

    user, _ = User.objects.get_or_create(username="testuser")
    access_token = generate_access_token(user.pk)
    case.call_and_validate(headers={"Authorization": f"Bearer {access_token}"})
