import os
from unittest.mock import MagicMock, patch

from django.test import TestCase
from PIL import Image

from wallpapers import models
from wallpapers.services import colors
from wallpapers.services.colors import ColorCluster, sync_colors


def test_extract_dominant_colors():
    with open(os.path.join(os.path.dirname(__file__), "assets/fff.png"), "rb") as fp:
        image = Image.open(fp)
        vector = colors.extract_dominant_colors(image)
        assert vector == [
            colors.ColorCluster(
                r=(224),
                g=(114),
                b=(156),
                proportion=(0.963623046875),
            ),
            colors.ColorCluster(
                r=(235),
                g=(164),
                b=(191),
                proportion=(0.01318359375),
            ),
            colors.ColorCluster(
                r=(231),
                g=(149),
                b=(181),
                proportion=(0.008544921875),
            ),
            colors.ColorCluster(
                r=(227),
                g=(128),
                b=(166),
                proportion=(0.008056640625),
            ),
            colors.ColorCluster(
                r=(238),
                g=(181),
                b=(203),
                proportion=(0.006591796875),
            ),
        ]


class TestSyncColorsMain(TestCase):
    """测试 sync_colors.main 函数"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试壁纸
        self.wallpaper1 = models.WallpaperImage.objects.create(
            url="https://example.com/test1.jpg",
            format="jpg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=2073600,
            filesize=500000,
            content_md5="test_md5_1",
        )

        self.wallpaper2 = models.WallpaperImage.objects.create(
            url="https://example.com/test2.jpg",
            format="jpg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=2073600,
            filesize=600000,
            content_md5="test_md5_2",
        )

        # 为 wallpaper2 创建已存在的颜色聚类，模拟已处理过的壁纸
        models.KMeansColorCluster.objects.create(
            wallpaper=self.wallpaper2, r=255, g=0, b=0, proportion=0.5
        )

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_success(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试成功处理壁纸的情况"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取的颜色
        mock_colors = [
            ColorCluster(r=255, g=0, b=0, proportion=0.4),
            ColorCluster(r=0, g=255, b=0, proportion=0.3),
            ColorCluster(r=0, g=0, b=255, proportion=0.3),
        ]
        mock_extract_colors.return_value = mock_colors

        # 执行主函数
        sync_colors()

        # 验证只处理了没有颜色聚类的壁纸
        mock_download_url.assert_called_once_with(self.wallpaper1.url)
        # 验证 Image.open 被调用，但不检查具体参数（因为 BytesIO 对象每次都不同）
        mock_image_open.assert_called_once()
        mock_extract_colors.assert_called_once_with(mock_image)

        # 验证创建了颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 3)

        # 验证颜色数据正确
        cluster_data = [
            (c.r, c.g, c.b, c.proportion) for c in clusters.order_by("r", "g", "b")
        ]
        expected_data = [(0, 0, 255, 0.3), (0, 255, 0, 0.3), (255, 0, 0, 0.4)]
        self.assertEqual(cluster_data, expected_data)

    @patch("wallpapers.services.colors.download_url")
    def test_main_download_failed(self, mock_download_url):
        """测试下载失败的情况"""
        # 模拟下载失败
        mock_download_url.return_value = None

        # 执行主函数
        with self.assertLogs("wallpapers.services.colors", level="WARNING") as log:
            sync_colors()

        # 验证记录了警告日志
        self.assertIn("下载壁纸失败", log.output[0])
        self.assertIn(self.wallpaper1.url, log.output[0])

        # 验证没有创建颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 0)

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_empty_colors(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试提取到空颜色列表的情况"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取到空颜色列表
        mock_extract_colors.return_value = []

        # 执行主函数
        sync_colors()

        # 验证没有创建颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 0)

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_bulk_create_ignore_conflicts(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试 bulk_create 的 ignore_conflicts 参数"""
        # 先为 wallpaper1 创建一个颜色聚类
        models.KMeansColorCluster.objects.create(
            wallpaper=self.wallpaper1, r=255, g=0, b=0, proportion=0.5
        )

        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取的颜色（包含重复的颜色）
        mock_colors = [
            ColorCluster(r=255, g=0, b=0, proportion=0.4),  # 重复颜色
            ColorCluster(r=0, g=255, b=0, proportion=0.3),  # 新颜色
        ]
        mock_extract_colors.return_value = mock_colors

        # 删除已有的颜色聚类，让 wallpaper1 重新被处理
        models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1).delete()

        # 执行主函数
        sync_colors()

        # 验证创建了颜色聚类记录，ignore_conflicts 确保不会因重复而报错
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 2)

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.Image.open")
    @patch("wallpapers.services.colors.download_url")
    def test_main_only_processes_wallpapers_without_colors(
        self, mock_download_url, mock_image_open, mock_extract_colors
    ):
        """测试只处理没有颜色聚类的壁纸"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取的颜色
        mock_colors = [ColorCluster(r=255, g=0, b=0, proportion=0.4)]
        mock_extract_colors.return_value = mock_colors

        # 执行主函数
        sync_colors()

        # 验证只调用了 wallpaper1 的下载（wallpaper2 已有颜色聚类）
        mock_download_url.assert_called_once_with(self.wallpaper1.url)

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_logging(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试日志记录"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取的颜色
        mock_colors = [ColorCluster(r=255, g=0, b=0, proportion=0.4)]
        mock_extract_colors.return_value = mock_colors

        # 执行主函数并捕获日志
        with self.assertLogs("wallpapers.services.colors", level="INFO") as log:
            sync_colors()

        # 验证记录了处理日志
        self.assertIn("正在处理壁纸", log.output[0])
        self.assertIn(self.wallpaper1.url, log.output[0])

    @patch("wallpapers.services.colors.Image.open")
    @patch("wallpapers.services.colors.download_url")
    def test_main_image_open_exception(self, mock_download_url, mock_image_open):
        """测试图片打开异常的情况"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟图片打开异常
        mock_image_open.side_effect = Exception("Invalid image format")

        # 执行主函数，应该不会崩溃
        with self.assertRaises(Exception):
            sync_colors()

        # 验证没有创建颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 0)

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_extract_colors_exception(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试颜色提取异常的情况"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟颜色提取异常
        mock_extract_colors.side_effect = Exception("Color extraction failed")

        # 执行主函数，应该不会崩溃
        with self.assertRaises(Exception):
            sync_colors()

        # 验证没有创建颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 0)

    def test_main_no_wallpapers_to_process(self):
        """测试没有需要处理的壁纸的情况"""
        # 为所有壁纸都创建颜色聚类，这样就没有需要处理的壁纸了
        models.KMeansColorCluster.objects.create(
            wallpaper=self.wallpaper1, r=100, g=100, b=100, proportion=1.0
        )

        with patch("wallpapers.services.colors.download_url") as mock_download_url:
            # 执行主函数
            sync_colors()

            # 验证没有调用下载函数
            mock_download_url.assert_not_called()

    @patch("wallpapers.services.colors.extract_dominant_colors")
    @patch("wallpapers.services.colors.download_url")
    @patch("wallpapers.services.colors.Image.open")
    def test_main_large_number_of_colors(
        self, mock_image_open, mock_download_url, mock_extract_colors
    ):
        """测试提取大量颜色的情况"""
        # 模拟下载成功
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image_open.return_value = mock_image

        # 模拟提取大量颜色（超过通常的5个）
        mock_colors = [
            ColorCluster(r=i * 10, g=i * 20, b=i * 30, proportion=0.1)
            for i in range(10)
        ]
        mock_extract_colors.return_value = mock_colors

        # 执行主函数
        sync_colors()

        # 验证创建了所有颜色聚类记录
        clusters = models.KMeansColorCluster.objects.filter(wallpaper=self.wallpaper1)
        self.assertEqual(clusters.count(), 10)
