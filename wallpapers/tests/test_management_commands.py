import logging
from io import String<PERSON>
from unittest import mock

from django.core.management import call_command
from django.test import TestCase

from wallpapers.models import WallpaperImage


class SyncToQdrantCommandTest(TestCase):
    """测试 synctoqdrant 管理命令"""

    def setUp(self):
        """设置测试数据"""
        self.wallpaper1 = WallpaperImage.objects.create(
            url="https://example.com/image1.jpg",
            format="jpg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=2073600,
            filesize=1024000,
            content_md5="abc123def456",
        )
        self.wallpaper2 = WallpaperImage.objects.create(
            url="https://example.com/image2.png",
            format="png",
            width=2560,
            height=1440,
            aspect_ratio=1.78,
            pixels=3686400,
            filesize=2048000,
            content_md5="def456ghi789",
        )

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_success(self, mock_set_level, mock_sync_wallpaper):
        """测试命令成功执行"""
        out = StringIO()

        call_command("synctoqdrant", stdout=out)

        # 验证日志级别设置
        mock_set_level.assert_called_once_with(logging.INFO)

        # 验证每个壁纸都被同步
        assert mock_sync_wallpaper.call_count == 2
        mock_sync_wallpaper.assert_any_call(self.wallpaper1)
        mock_sync_wallpaper.assert_any_call(self.wallpaper2)

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_with_sync_error(self, mock_set_level, mock_sync_wallpaper):
        """测试同步过程中出现异常的情况"""
        # 模拟第一个壁纸同步失败
        mock_sync_wallpaper.side_effect = [
            Exception("同步失败"),
            None,  # 第二个成功
        ]

        out = StringIO()

        # 命令应该继续执行，不会因为单个失败而中断
        call_command("synctoqdrant", stdout=out)

        # 验证仍然尝试同步所有壁纸
        assert mock_sync_wallpaper.call_count == 2

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_with_no_wallpapers(self, mock_set_level, mock_sync_wallpaper):
        """测试没有壁纸时的情况"""
        # 删除所有壁纸
        WallpaperImage.objects.all().delete()

        out = StringIO()

        call_command("synctoqdrant", stdout=out)

        # 验证日志级别设置
        mock_set_level.assert_called_once_with(logging.INFO)

        # 验证没有调用同步函数
        mock_sync_wallpaper.assert_not_called()

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    @mock.patch("wallpapers.management.commands.synctoqdrant.logger")
    def test_command_logs_exceptions(
        self, mock_logger, mock_set_level, mock_sync_wallpaper
    ):
        """测试异常被正确记录"""
        error = Exception("测试异常")
        mock_sync_wallpaper.side_effect = error

        out = StringIO()

        call_command("synctoqdrant", stdout=out)

        # 验证异常被记录
        assert mock_logger.exception.call_count == 2
        mock_logger.exception.assert_any_call(
            "同步壁纸到 Qdrant 失败 wallpaper=%r", self.wallpaper1
        )
        mock_logger.exception.assert_any_call(
            "同步壁纸到 Qdrant 失败 wallpaper=%r", self.wallpaper2
        )

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_uses_iterator(self, mock_set_level, mock_sync_wallpaper):
        """测试命令使用 iterator() 方法以节省内存"""
        with mock.patch.object(WallpaperImage.objects, "iterator") as mock_iterator:
            mock_iterator.return_value = iter([self.wallpaper1, self.wallpaper2])

            out = StringIO()
            call_command("synctoqdrant", stdout=out)

            # 验证使用了 iterator
            mock_iterator.assert_called_once()

            # 验证同步了正确的壁纸
            assert mock_sync_wallpaper.call_count == 2

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_handles_large_dataset(self, mock_set_level, mock_sync_wallpaper):
        """测试命令能够处理大量数据"""
        # 创建更多测试数据
        wallpapers = []
        for i in range(10):
            wallpaper = WallpaperImage.objects.create(
                url=f"https://example.com/image{i}.jpg",
                format="jpg",
                width=1920,
                height=1080,
                aspect_ratio=1.78,
                pixels=2073600,
                filesize=1024000,
                content_md5=f"test{i:06d}",
            )
            wallpapers.append(wallpaper)

        out = StringIO()
        call_command("synctoqdrant", stdout=out)

        # 验证所有壁纸都被处理（包括 setUp 中的 2 个）
        assert mock_sync_wallpaper.call_count == 12

    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_partial_failure_continues(
        self, mock_set_level, mock_sync_wallpaper
    ):
        """测试部分失败时命令继续执行"""
        # 模拟交替成功和失败
        mock_sync_wallpaper.side_effect = [
            None,  # 成功
            Exception("网络错误"),  # 失败
        ]

        out = StringIO()

        # 命令应该完成而不抛出异常
        call_command("synctoqdrant", stdout=out)

        # 验证两个壁纸都被尝试同步
        assert mock_sync_wallpaper.call_count == 2


class SyncToQdrantCommandUnitTest(TestCase):
    """synctoqdrant 命令的单元测试"""

    def test_command_import(self):
        """测试命令可以正确导入"""
        from wallpapers.management.commands.synctoqdrant import Command

        command = Command()
        assert hasattr(command, "handle")

    def test_command_is_django_command(self):
        """测试命令继承自 Django BaseCommand"""
        from django.core.management.base import BaseCommand

        from wallpapers.management.commands.synctoqdrant import Command

        assert issubclass(Command, BaseCommand)

    @mock.patch("wallpapers.models.WallpaperImage.objects.iterator")
    @mock.patch("wallpapers.services.qdrant.sync_wallpaper")
    @mock.patch("project.log.ConsoleFilter.setLevel")
    def test_command_handle_method_signature(
        self, mock_set_level, mock_sync_wallpaper, mock_iterator
    ):
        """测试 handle 方法接受正确的参数"""
        from wallpapers.management.commands.synctoqdrant import Command

        mock_iterator.return_value = iter([])

        command = Command()

        # 测试不同的参数组合
        command.handle()
        command.handle(verbosity=2)
        command.handle(verbosity=1, traceback=True)

        # 验证日志级别被设置
        assert mock_set_level.call_count >= 3
