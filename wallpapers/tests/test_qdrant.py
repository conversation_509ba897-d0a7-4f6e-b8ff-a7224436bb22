# type: ignore
import pytest
from qdrant_client import models as qm

from wallpapers.services.qdrant import _build_aspect_ratio_filter
from wallpapers.utils import numfilter


class TestBuildAspectRatioFilter:
    """测试 _build_aspect_ratio_filter 函数"""

    def test_single_constraint_eq(self):
        """测试单个相等约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.EQ, value=1.78)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 验证结构
        assert isinstance(result, qm.Filter)
        assert result.should is not None
        assert len(result.should) == 1  # type: ignore

        # 验证第一个组
        group = result.should[0]  # type: ignore
        assert isinstance(group, qm.Filter)
        assert group.must is not None
        assert len(group.must) == 1  # type: ignore

        # 验证条件
        condition = group.must[0]  # type: ignore
        assert isinstance(condition, qm.FieldCondition)
        assert condition.key == "aspect_ratio"
        assert condition.range is not None
        # 验证 epsilon 范围
        epsilon = 1e-6
        assert condition.range.gte == pytest.approx(1.78 - epsilon)
        assert condition.range.lte == pytest.approx(1.78 + epsilon)

    def test_single_constraint_gte(self):
        """测试单个大于等于约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.5)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        assert isinstance(result, qm.Filter)
        assert len(result.should) == 1  # type: ignore

        group = result.should[0]  # type: ignore
        assert len(group.must) == 1  # type: ignore

        condition = group.must[0]  # type: ignore
        assert condition.key == "aspect_ratio"
        assert condition.range.gte == 1.5
        assert condition.range.lte is None

    def test_single_constraint_lte(self):
        """测试单个小于等于约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.LTE, value=2.0)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]  # type: ignore
        assert condition.range.lte == 2.0
        assert condition.range.gte is None

    def test_single_constraint_gt(self):
        """测试单个大于约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.GT, value=1.0)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]  # type: ignore
        assert condition.range.gt == 1.0
        assert condition.range.gte is None

    def test_single_constraint_lt(self):
        """测试单个小于约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.LT, value=3.0)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]  # type: ignore
        assert condition.range.lt == 3.0
        assert condition.range.lte is None

    def test_multiple_constraints_in_single_group(self):
        """测试单个组内的多个约束（AND 关系）"""
        constraints = [
            [
                numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.5),
                numfilter.Constraint(operator=numfilter.Operator.LT, value=2.0),
            ]
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 验证结构：一个组，两个条件
        assert len(result.should) == 1  # type: ignore
        group = result.should[0]  # type: ignore
        assert len(group.must) == 2  # type: ignore

        # 验证第一个条件 (>=1.5)
        condition1 = group.must[0]  # type: ignore
        assert condition1.key == "aspect_ratio"
        assert condition1.range.gte == 1.5

        # 验证第二个条件 (<2.0)
        condition2 = group.must[1]  # type: ignore
        assert condition2.key == "aspect_ratio"
        assert condition2.range.lt == 2.0

    def test_multiple_constraint_groups(self):
        """测试多个约束组（OR 关系）"""
        constraints = [
            [
                numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.5),
                numfilter.Constraint(operator=numfilter.Operator.LT, value=2.0),
            ],
            [
                numfilter.Constraint(operator=numfilter.Operator.GTE, value=0.5),
                numfilter.Constraint(operator=numfilter.Operator.LT, value=1.0),
            ],
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 验证结构：两个组
        assert len(result.should) == 2

        # 验证第一个组 (>=1.5 AND <2.0)
        group1 = result.should[0]
        assert len(group1.must) == 2
        assert group1.must[0].range.gte == 1.5
        assert group1.must[1].range.lt == 2.0

        # 验证第二个组 (>=0.5 AND <1.0)
        group2 = result.should[1]
        assert len(group2.must) == 2
        assert group2.must[0].range.gte == 0.5
        assert group2.must[1].range.lt == 1.0

    def test_complex_mixed_constraints(self):
        """测试复杂的混合约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.EQ, value=1.78)],
            [
                numfilter.Constraint(operator=numfilter.Operator.GT, value=2.0),
                numfilter.Constraint(operator=numfilter.Operator.LTE, value=3.0),
            ],
            [numfilter.Constraint(operator=numfilter.Operator.LT, value=0.8)],
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 验证结构：三个组
        assert len(result.should) == 3

        # 第一个组：相等约束
        group1 = result.should[0]
        assert len(group1.must) == 1
        condition1 = group1.must[0]
        epsilon = 1e-6
        assert condition1.range.gte == pytest.approx(1.78 - epsilon)
        assert condition1.range.lte == pytest.approx(1.78 + epsilon)

        # 第二个组：范围约束
        group2 = result.should[1]
        assert len(group2.must) == 2
        assert group2.must[0].range.gt == 2.0
        assert group2.must[1].range.lte == 3.0

        # 第三个组：小于约束
        group3 = result.should[2]
        assert len(group3.must) == 1
        assert group3.must[0].range.lt == 0.8

    def test_empty_constraints(self):
        """测试空约束列表"""
        constraints = []

        result = _build_aspect_ratio_filter(constraints)

        # 空约束应该返回空的 should 列表
        assert isinstance(result, qm.Filter)
        assert result.should == []

    def test_constraint_group_with_no_conditions(self):
        """测试包含空组的约束"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.0)],
            [],  # 空组
            [numfilter.Constraint(operator=numfilter.Operator.LT, value=2.0)],
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 空组应该被忽略，只有两个有效组
        assert len(result.should) == 2
        assert result.should[0].must[0].range.gte == 1.0
        assert result.should[1].must[0].range.lt == 2.0

    def test_integer_values(self):
        """测试整数值约束"""
        constraints = [[numfilter.Constraint(operator=numfilter.Operator.EQ, value=2)]]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]
        epsilon = 1e-6
        assert condition.range.gte == pytest.approx(2.0 - epsilon)
        assert condition.range.lte == pytest.approx(2.0 + epsilon)

    def test_very_small_values(self):
        """测试非常小的值"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.GTE, value=0.001)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]
        assert condition.range.gte == 0.001

    def test_very_large_values(self):
        """测试非常大的值"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.LTE, value=100.0)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]
        assert condition.range.lte == 100.0

    def test_precision_values(self):
        """测试高精度浮点数"""
        constraints = [
            [numfilter.Constraint(operator=numfilter.Operator.EQ, value=1.7777777)]
        ]

        result = _build_aspect_ratio_filter(constraints)

        condition = result.should[0].must[0]
        epsilon = 1e-6
        assert condition.range.gte == pytest.approx(1.7777777 - epsilon)
        assert condition.range.lte == pytest.approx(1.7777777 + epsilon)

    def test_all_operators_in_one_group(self):
        """测试在一个组中使用所有操作符"""
        constraints = [
            [
                numfilter.Constraint(operator=numfilter.Operator.GT, value=1.0),
                numfilter.Constraint(operator=numfilter.Operator.LT, value=3.0),
                numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.1),
                numfilter.Constraint(operator=numfilter.Operator.LTE, value=2.9),
            ]
        ]

        result = _build_aspect_ratio_filter(constraints)

        # 验证结构：一个组，四个条件
        assert len(result.should) == 1
        group = result.should[0]
        assert len(group.must) == 4

        # 验证每个条件
        conditions = group.must
        assert conditions[0].range.gt == 1.0
        assert conditions[1].range.lt == 3.0
        assert conditions[2].range.gte == 1.1
        assert conditions[3].range.lte == 2.9
