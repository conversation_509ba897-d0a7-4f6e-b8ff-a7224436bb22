import zangar as z
from zangar.compilation import OpenAPI30Compiler

from wallpapers.utils import query_on_demand as qd


class C(qd.Model):
    g = qd.Field(z.str())


class B(qd.Model):
    d = qd.Field(z.str())
    e = qd.Field(z.str(), include_in_keys=False)
    f = C()


class A(qd.Model):
    a = qd.Field(z.str())
    b = qd.Field(z.str())
    c = qd.Field(z.str(), include_in_keys=False)
    bar = B()
    bar2 = B(many=True)


# 测试继承的基类
class BaseModel(qd.Model):
    base_field = qd.Field(z.str())
    base_optional = qd.Field(z.str(), include_in_keys=False)


class ChildModel(BaseModel):
    child_field = qd.Field(z.int())
    # 覆盖父类字段
    base_field = qd.Field(z.int())


class GrandChildModel(ChildModel):
    grandchild_field = qd.Field(z.bool())
    nested = C()


def test_paths():
    assert A.bar.f.g.__metadata__.paths() == ["bar", "f", "g"]
    assert A.bar2.f.g.__metadata__.paths() == ["bar2", "f", "g"]
    assert B.f.g.__metadata__.fullname() == "f.g"


def test_flatten():
    assert qd.flatten(A) == [
        "a",
        "b",
        "c",
        "bar.d",
        "bar.e",
        "bar.f.g",
        "bar2.d",
        "bar2.e",
        "bar2.f.g",
    ]


class TestFlattened:
    def test_contains(self):
        assert qd.FieldSelector(["a", "bar.d"]).contains(A.a)
        assert qd.FieldSelector(["a", "bar.d"]).contains(A.bar.d)
        assert qd.FieldSelector(["a", "bar.d"]).contains(A.bar)
        assert not qd.FieldSelector(["a", "bar.d"]).contains(A.bar.e)
        assert not qd.FieldSelector(["a", "bar.d"]).contains(A.b)
        assert qd.FieldSelector(["bar.f.g"]).contains(A.bar.f.g)

    def test_keys(self):
        assert qd.FieldSelector(["a", "bar.d"]).keys(A) == ["a", "bar"]
        assert qd.FieldSelector(["a", "bar.d"]).keys(A.bar) == ["d"]
        assert qd.FieldSelector(["a", "bar.d", "bar.e"]).keys(A.bar) == ["d"]


class TestStruct:
    def test(self):
        struct = qd.struct(A, qd.FieldSelector(["a", "bar.d"]))
        assert OpenAPI30Compiler().compile(struct) == {
            "type": "object",
            "properties": {
                "a": {
                    "type": "string",
                },
                "bar": {
                    "type": "object",
                    "properties": {
                        "d": {
                            "type": "string",
                        }
                    },
                    "required": ["d"],
                },
            },
            "required": ["a", "bar"],
        }

    def test_optional(self):
        struct = qd.struct(A, qd.FieldSelector(["a", "bar.d", "bar2.e"]), optional=True)
        assert OpenAPI30Compiler().compile(struct) == {
            "type": "object",
            "properties": {
                "a": {
                    "type": "string",
                },
                "bar": {
                    "type": "object",
                    "properties": {
                        "d": {
                            "type": "string",
                        }
                    },
                },
                "bar2": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "e": {
                                "type": "string",
                            },
                        },
                    },
                },
            },
        }


class TestInheritance:
    """测试 Model 继承功能"""

    def test_inherit_fields(self):
        """测试子类继承父类字段"""
        # 检查子类包含父类字段
        assert "base_field" in ChildModel.__fields__
        assert "base_optional" in ChildModel.__fields__
        assert "child_field" in ChildModel.__fields__

    def test_override_fields(self):
        """测试子类覆盖父类字段"""
        # 子类覆盖了 base_field，应该是 int 类型而不是 str
        base_field = BaseModel.__fields__["base_field"]
        child_field = ChildModel.__fields__["base_field"]

        # 验证类型不同（父类是 str，子类是 int）
        assert base_field.__metadata__.schema != child_field.__metadata__.schema

    def test_multi_level_inheritance(self):
        """测试多级继承"""
        # 孙子类应该包含所有祖先的字段
        assert "base_field" in GrandChildModel.__fields__
        assert "base_optional" in GrandChildModel.__fields__
        assert "child_field" in GrandChildModel.__fields__
        assert "grandchild_field" in GrandChildModel.__fields__
        assert "nested" in GrandChildModel.__fields__

    def test_flatten_inherited_model(self):
        """测试继承模型的扁平化"""
        flattened = qd.flatten(GrandChildModel)
        expected = [
            "base_field",
            "base_optional",
            "child_field",
            "grandchild_field",
            "nested.g",
        ]
        assert flattened == expected

    def test_struct_with_inheritance(self):
        """测试继承模型的结构生成"""
        struct = qd.struct(ChildModel, qd.FieldSelector(["base_field", "child_field"]))
        compiled = OpenAPI30Compiler().compile(struct)

        assert "base_field" in compiled["properties"]
        assert "child_field" in compiled["properties"]
        # base_field 应该是 int 类型（子类覆盖的）
        assert compiled["properties"]["base_field"]["type"] == "integer"
        assert compiled["properties"]["child_field"]["type"] == "integer"

    def test_field_selector_with_inheritance(self):
        """测试字段选择器与继承的配合"""
        selector = qd.FieldSelector(["base_field", "child_field", "nested.g"])

        # 测试包含检查
        assert selector.contains(GrandChildModel.base_field)
        assert selector.contains(GrandChildModel.child_field)
        assert selector.contains(GrandChildModel.nested.g)
        assert not selector.contains(GrandChildModel.base_optional)
        assert not selector.contains(GrandChildModel.grandchild_field)

        # 测试键获取
        keys = selector.keys(GrandChildModel)
        assert "base_field" in keys
        assert "child_field" in keys
        assert "nested" in keys
