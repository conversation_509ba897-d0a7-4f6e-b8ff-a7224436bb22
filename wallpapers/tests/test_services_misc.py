"""
测试 wallpapers.services.misc 模块
"""

from unittest.mock import MagicMock, patch

import httpx
from django.test import TestCase

from wallpapers import models
from wallpapers.services.misc import download_url, get_top_tags, random_uploader


class TestDownloadUrl(TestCase):
    """测试 download_url 函数"""

    @patch("wallpapers.services.misc.httpx.get")
    def test_download_url_success(self, mock_httpx_get):
        """测试成功下载"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.content = b"fake_content"
        mock_response.raise_for_status.return_value = None
        mock_httpx_get.return_value = mock_response

        url = "https://example.com/test.jpg"
        result = download_url(url)

        self.assertEqual(result, b"fake_content")
        mock_httpx_get.assert_called_once_with(url)
        mock_response.raise_for_status.assert_called_once()

    @patch("wallpapers.services.misc.httpx.get")
    def test_download_url_request_error(self, mock_httpx_get):
        """测试请求错误"""
        mock_httpx_get.side_effect = httpx.RequestError("网络错误")

        url = "https://example.com/test.jpg"
        result = download_url(url)

        self.assertIsNone(result)

    @patch("wallpapers.services.misc.httpx.get")
    def test_download_url_http_error(self, mock_httpx_get):
        """测试 HTTP 错误"""
        mock_response = MagicMock()
        mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
            "404 Not Found", request=MagicMock(), response=MagicMock()
        )
        mock_httpx_get.return_value = mock_response

        url = "https://example.com/test.jpg"
        result = download_url(url)

        self.assertIsNone(result)


class TestGetTopTags(TestCase):
    """测试 get_top_tags 函数"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试壁纸
        self.wallpaper1 = models.WallpaperImage.objects.create(
            url="https://example.com/1.jpg",
            format="jpeg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=1920 * 1080,
            filesize=100000,
            content_md5="test_md5_1",
        )
        self.wallpaper2 = models.WallpaperImage.objects.create(
            url="https://example.com/2.jpg",
            format="jpeg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=1920 * 1080,
            filesize=100000,
            content_md5="test_md5_2",
        )

        # 创建测试标签
        models.CLIPTag.objects.create(wallpaper=self.wallpaper1, name="自然", score=0.8)
        models.CLIPTag.objects.create(wallpaper=self.wallpaper2, name="自然", score=0.7)
        models.CLIPTag.objects.create(wallpaper=self.wallpaper1, name="城市", score=0.6)
        # 低分标签，不应该被包含
        models.CLIPTag.objects.create(wallpaper=self.wallpaper1, name="动物", score=0.3)

    def test_get_top_tags(self):
        """测试获取热门标签"""
        result = get_top_tags(5)
        result_list = list(result)

        # 验证结果格式
        self.assertIsInstance(result_list, list)
        self.assertTrue(len(result_list) > 0)

        # 验证第一个结果是"自然"（有2个壁纸）
        self.assertEqual(result_list[0]["name"], "自然")
        self.assertEqual(result_list[0]["count"], 2)

        # 验证第二个结果是"城市"（有1个壁纸）
        self.assertEqual(result_list[1]["name"], "城市")
        self.assertEqual(result_list[1]["count"], 1)

        # 验证低分标签不被包含
        tag_names = [item["name"] for item in result_list]
        self.assertNotIn("动物", tag_names)

    def test_get_top_tags_limit(self):
        """测试限制返回数量"""
        result = list(get_top_tags(1))
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["name"], "自然")


class TestRandomUploader(TestCase):
    """测试 random_uploader 函数"""

    def test_random_uploader_exists(self):
        """测试存在上传者时的情况"""
        # 创建测试上传者
        uploader1 = models.Uploader.objects.create(name="test_user1")
        uploader2 = models.Uploader.objects.create(name="test_user2")

        result = random_uploader()

        # 验证返回的是其中一个上传者
        self.assertIn(result, [uploader1, uploader2])

    def test_random_uploader_with_email_excluded(self):
        """测试有邮箱的上传者被排除"""
        # 创建有邮箱的上传者（应该被排除）
        models.Uploader.objects.create(name="user_with_email", email="<EMAIL>")

        # 创建没有邮箱的上传者
        uploader_no_email = models.Uploader.objects.create(name="user_no_email")

        result = random_uploader()

        # 验证返回的是没有邮箱的上传者
        self.assertEqual(result, uploader_no_email)

    def test_random_uploader_none_exists(self):
        """测试不存在符合条件的上传者时的情况"""
        # 只创建有邮箱的上传者
        models.Uploader.objects.create(name="user_with_email", email="<EMAIL>")

        result = random_uploader()

        # 验证返回 None
        self.assertIsNone(result)

    def test_random_uploader_empty_database(self):
        """测试数据库为空时的情况"""
        result = random_uploader()
        self.assertIsNone(result)
