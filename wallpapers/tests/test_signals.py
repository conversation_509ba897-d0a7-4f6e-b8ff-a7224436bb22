"""
测试壁纸信号处理功能（包括创建和删除）
"""

from unittest.mock import patch

from django.test import TestCase, override_settings

from wallpapers.models import WallpaperImage
from wallpapers.tasks import delete_wallpaper_from_qdrant


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
)
class TestWallpaperSignals(TestCase):
    """测试壁纸相关信号处理"""

    def setUp(self):
        """设置测试数据"""
        self.wallpaper_data = {
            "url": "https://example.com/test.jpg",
            "width": 1920,
            "height": 1080,
            "format": "JPEG",
            "filesize": 1024000,
            "content_md5": "test_md5_hash",
            "aspect_ratio": 1920 / 1080,
            "pixels": 1920 * 1080,
        }

    @patch("wallpapers.tasks.delete_wallpaper_from_qdrant.delay")
    def test_delete_signal_triggered_on_wallpaper_deletion(self, mock_delete_task):
        """测试删除壁纸时触发删除信号"""
        # 先创建壁纸
        wallpaper = WallpaperImage.objects.create(**self.wallpaper_data)
        content_md5 = wallpaper.content_md5

        # 删除壁纸
        wallpaper.delete()

        # 验证删除任务被调用，传入的是 content_md5
        mock_delete_task.assert_called_once_with(content_md5)


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
)
class TestAddToQdrantTask(TestCase):
    """测试添加到 Qdrant 的任务"""

    def setUp(self):
        """设置测试数据"""
        self.wallpaper_data = {
            "url": "https://example.com/test.jpg",
            "width": 1920,
            "height": 1080,
            "format": "JPEG",
            "filesize": 1024000,
            "content_md5": "test_md5_hash_unique",
            "aspect_ratio": 1920 / 1080,
            "pixels": 1920 * 1080,
        }


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
)
class TestDeleteFromQdrantTask(TestCase):
    """测试从 Qdrant 删除的任务"""

    @patch("wallpapers.services.qdrant.client.delete")
    def test_delete_wallpaper_from_qdrant_success(self, mock_qdrant_delete):
        """测试成功从 Qdrant 删除"""
        content_md5 = "test_md5_hash_for_deletion"

        # 直接执行删除任务
        delete_wallpaper_from_qdrant(content_md5)

        # 验证调用
        mock_qdrant_delete.assert_called_once_with(
            "CLIP_ViT-L-14", points_selector=["test_md5_hash_for_deletion"]
        )

    @patch("wallpapers.services.qdrant.delete_data_by_id")
    def test_delete_wallpaper_from_qdrant_with_qdrant_error(self, mock_qdrant_delete):
        """测试 Qdrant 服务出错时的处理"""
        content_md5 = "test_md5_hash_for_error"

        # 模拟 Qdrant 服务抛出异常
        mock_qdrant_delete.side_effect = Exception("Qdrant 删除服务错误")

        # 验证任务会抛出异常
        with self.assertRaises(Exception) as context:
            delete_wallpaper_from_qdrant(content_md5)

        self.assertIn("Qdrant 删除服务错误", str(context.exception))


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
)
class TestSignalIntegration(TestCase):
    """测试信号处理的集成功能"""

    def setUp(self):
        """设置测试数据"""
        self.wallpaper_data = {
            "url": "https://example.com/test.jpg",
            "width": 1920,
            "height": 1080,
            "format": "JPEG",
            "filesize": 1024000,
            "content_md5": "test_md5_hash_integration",
            "aspect_ratio": 1920 / 1080,
            "pixels": 1920 * 1080,
        }

    @patch("wallpapers.services.qdrant.client.delete")
    def test_end_to_end_wallpaper_deletion_and_qdrant_removal(self, mock_qdrant_delete):
        """测试端到端的壁纸删除和 Qdrant 移除流程"""
        # 创建壁纸
        wallpaper = WallpaperImage.objects.create(**self.wallpaper_data)

        # 删除壁纸（这会触发信号）
        wallpaper.delete()

        # 验证整个流程
        mock_qdrant_delete.assert_called_once_with(
            "CLIP_ViT-L-14", points_selector=[wallpaper.content_md5]
        )
