from unittest import mock

import pytest
from django.test import TestCase

from wallpapers import models
from wallpapers.services import qdrant


class SyncWallpaperCoreTest(TestCase):
    """测试 sync_wallpaper 函数的核心功能"""

    def setUp(self):
        """设置测试数据"""
        self.wallpaper = models.WallpaperImage.objects.create(
            url="https://example.com/test-image.jpg",
            format="jpg",
            width=1920,
            height=1080,
            aspect_ratio=1.78,
            pixels=2073600,
            filesize=1024000,
            content_md5="test123abc456def789012345678901",  # 32位MD5
        )

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    @mock.patch("clip_service.api.encode_image_encode_image_post")
    @mock.patch("wallpapers.services.misc.download_url")
    def test_sync_wallpaper_success(
        self,
        mock_download_url,
        mock_clip_service,
        mock_upsert,
        mock_retrieve,
    ):
        """测试成功同步壁纸到 Qdrant"""
        # 模拟 Qdrant 中不存在该壁纸
        mock_retrieve.return_value = []

        # 模拟下载图片成功
        mock_image_content = b"fake_image_content"
        mock_download_url.return_value = mock_image_content

        # 模拟 CLIP 服务返回向量
        mock_vector_response = mock.MagicMock()
        mock_vector_response.vector = [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_clip_service.return_value = mock_vector_response

        # 执行同步
        qdrant.sync_wallpaper(self.wallpaper)

        # 验证调用顺序和参数
        mock_retrieve.assert_called_once_with(
            qdrant.COLLECTION_NAME, [self.wallpaper.content_md5]
        )
        mock_download_url.assert_called_once_with(self.wallpaper.url)
        mock_clip_service.assert_called_once_with(mock_image_content)
        mock_upsert.assert_called_once_with(self.wallpaper, [0.1, 0.2, 0.3, 0.4, 0.5])

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    def test_sync_wallpaper_already_exists(self, mock_retrieve):
        """测试壁纸已存在于 Qdrant 中的情况"""
        # 模拟 Qdrant 中已存在该壁纸
        mock_retrieve.return_value = [mock.MagicMock()]

        with mock.patch("wallpapers.services.misc.download_url") as mock_download_url:
            # 执行同步
            qdrant.sync_wallpaper(self.wallpaper)

            # 验证只检查了存在性，没有进行下载和向量化
            mock_retrieve.assert_called_once_with(
                qdrant.COLLECTION_NAME, [self.wallpaper.content_md5]
            )
            mock_download_url.assert_not_called()

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.misc.download_url")
    def test_sync_wallpaper_download_failed(self, mock_download_url, mock_retrieve):
        """测试下载图片失败的情况"""
        # 模拟 Qdrant 中不存在该壁纸
        mock_retrieve.return_value = []

        # 模拟下载失败
        mock_download_url.return_value = None

        with mock.patch(
            "clip_service.api.encode_image_encode_image_post"
        ) as mock_clip_service:
            # 执行同步
            qdrant.sync_wallpaper(self.wallpaper)

            # 验证下载被调用但 CLIP 服务没有被调用
            mock_download_url.assert_called_once_with(self.wallpaper.url)
            mock_clip_service.assert_not_called()

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    @mock.patch("clip_service.api.encode_image_encode_image_post")
    @mock.patch("wallpapers.services.misc.download_url")
    def test_sync_wallpaper_clip_service_error(
        self,
        mock_download_url,
        mock_clip_service,
        mock_upsert,
        mock_retrieve,
    ):
        """测试 CLIP 服务调用失败的情况"""
        # 模拟 Qdrant 中不存在该壁纸
        mock_retrieve.return_value = []

        # 模拟下载成功
        mock_download_url.return_value = b"fake_image_content"

        # 模拟 CLIP 服务抛出异常
        mock_clip_service.side_effect = Exception("CLIP 服务不可用")

        # 执行同步，应该抛出异常
        with pytest.raises(Exception, match="CLIP 服务不可用"):
            qdrant.sync_wallpaper(self.wallpaper)

        # 验证 upsert 没有被调用
        mock_upsert.assert_not_called()

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    @mock.patch("clip_service.api.encode_image_encode_image_post")
    @mock.patch("wallpapers.services.misc.download_url")
    def test_sync_wallpaper_upsert_error(
        self,
        mock_download_url,
        mock_clip_service,
        mock_upsert,
        mock_retrieve,
    ):
        """测试向 Qdrant 插入数据失败的情况"""
        # 模拟 Qdrant 中不存在该壁纸
        mock_retrieve.return_value = []

        # 模拟下载成功
        mock_download_url.return_value = b"fake_image_content"

        # 模拟 CLIP 服务成功
        mock_vector_response = mock.MagicMock()
        mock_vector_response.vector = [0.1, 0.2, 0.3]
        mock_clip_service.return_value = mock_vector_response

        # 模拟 upsert 失败
        mock_upsert.side_effect = Exception("Qdrant 连接失败")

        # 执行同步，应该抛出异常
        with pytest.raises(Exception, match="Qdrant 连接失败"):
            qdrant.sync_wallpaper(self.wallpaper)

        # 验证所有步骤都被调用了
        mock_retrieve.assert_called_once()
        mock_download_url.assert_called_once()
        mock_clip_service.assert_called_once()
        mock_upsert.assert_called_once()

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    @mock.patch("clip_service.api.encode_image_encode_image_post")
    @mock.patch("wallpapers.services.misc.download_url")
    @mock.patch("wallpapers.services.qdrant.logger")
    def test_sync_wallpaper_logging(
        self,
        mock_logger,
        mock_download_url,
        mock_clip_service,
        mock_upsert,
        mock_retrieve,
    ):
        """测试日志记录功能"""
        # 模拟成功场景
        mock_retrieve.return_value = []
        mock_download_url.return_value = b"fake_image_content"
        mock_vector_response = mock.MagicMock()
        mock_vector_response.vector = [0.1, 0.2, 0.3]
        mock_clip_service.return_value = mock_vector_response

        # 执行同步
        qdrant.sync_wallpaper(self.wallpaper)

        # 验证日志记录
        mock_logger.info.assert_any_call(
            "正在同步壁纸到 Qdrant wallpaper=%r", self.wallpaper
        )
        mock_logger.info.assert_any_call(
            "壁纸同步到 Qdrant 成功 wallpaper=%r", self.wallpaper
        )

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.logger")
    def test_sync_wallpaper_already_exists_logging(self, mock_logger, mock_retrieve):
        """测试壁纸已存在时的日志记录"""
        # 模拟壁纸已存在
        mock_retrieve.return_value = [mock.MagicMock()]

        # 执行同步
        qdrant.sync_wallpaper(self.wallpaper)

        # 验证日志记录
        mock_logger.info.assert_any_call(
            "正在同步壁纸到 Qdrant wallpaper=%r", self.wallpaper
        )
        mock_logger.info.assert_any_call(
            "壁纸已经存在 Qdrant 中, 更新 payload。 wallpaper=%r", self.wallpaper
        )

    def test_sync_wallpaper_with_none_wallpaper(self):
        """测试传入 None 的情况"""
        # 这应该抛出 AttributeError，因为 None 没有 content_md5 属性
        with pytest.raises(AttributeError):
            qdrant.sync_wallpaper(None)  # type: ignore

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    def test_sync_wallpaper_retrieve_exception(self, mock_retrieve):
        """测试 Qdrant retrieve 操作异常"""
        mock_retrieve.side_effect = Exception("Qdrant 连接超时")

        # 执行同步，应该抛出异常
        with pytest.raises(Exception, match="Qdrant 连接超时"):
            qdrant.sync_wallpaper(self.wallpaper)

    @mock.patch("wallpapers.services.qdrant.client.retrieve")
    @mock.patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    @mock.patch("clip_service.api.encode_image_encode_image_post")
    @mock.patch("wallpapers.services.misc.download_url")
    def test_sync_wallpaper_with_different_vector_sizes(
        self,
        mock_download_url,
        mock_clip_service,
        mock_upsert,
        mock_retrieve,
    ):
        """测试不同大小的向量"""
        # 模拟 Qdrant 中不存在该壁纸
        mock_retrieve.return_value = []
        mock_download_url.return_value = b"fake_image_content"

        # 测试不同大小的向量
        test_vectors = [
            [0.1] * 512,  # 512 维向量
            [0.2] * 768,  # 768 维向量
            [0.3] * 1024,  # 1024 维向量
        ]

        for vector in test_vectors:
            mock_vector_response = mock.MagicMock()
            mock_vector_response.vector = vector
            mock_clip_service.return_value = mock_vector_response

            # 重置 mock
            mock_upsert.reset_mock()

            # 执行同步
            qdrant.sync_wallpaper(self.wallpaper)

            # 验证向量被正确传递
            mock_upsert.assert_called_once_with(self.wallpaper, vector)
