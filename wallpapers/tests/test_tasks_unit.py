"""
单元测试壁纸任务功能
"""

from unittest.mock import MagicMock, patch

from django.test import TestCase, override_settings

from clip_service_client.models.encoding_image_response import EncodingImageResponse
from wallpapers import models
from wallpapers.models import UploaderWallpaper, WallpaperImage
from wallpapers.tasks import (
    create_wallpaper,
    delete_wallpaper_from_qdrant,
)


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
)
class TestTasksUnit(TestCase):
    """单元测试任务功能"""

    @patch("wallpapers.services.qdrant.delete_data_by_id")
    def test_delete_wallpaper_from_qdrant_success(self, mock_qdrant_delete):
        """测试成功从 Qdrant 删除"""
        content_md5 = "test_md5_hash_for_deletion"

        # 执行删除任务
        delete_wallpaper_from_qdrant(content_md5)

        # 验证调用
        mock_qdrant_delete.assert_called_once_with(id=content_md5)


class TestCreateWallpaperTask(TestCase):
    """测试创建壁纸任务"""

    @patch("wallpapers.services.misc.random_uploader")
    @patch("wallpapers.services.misc.download_url")
    @patch("PIL.Image.open")
    def test_create_wallpaper_success(
        self, mock_pil_open, mock_download_url, mock_random_uploader
    ):
        """测试成功创建壁纸"""
        # 模拟下载内容
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image.size = (1920, 1080)
        mock_pil_open.return_value = mock_image

        # 创建真实的上传者对象
        uploader = models.Uploader.objects.create(name="test_uploader")
        mock_random_uploader.return_value = uploader

        # 执行任务
        url = "https://example.com/test.jpg"
        create_wallpaper(url)

        # 验证壁纸被创建
        wallpaper = models.WallpaperImage.objects.get(url=url)
        self.assertEqual(wallpaper.format, "jpeg")
        self.assertEqual(wallpaper.width, 1920)
        self.assertEqual(wallpaper.height, 1080)
        self.assertEqual(wallpaper.aspect_ratio, 1920 / 1080)
        self.assertEqual(wallpaper.pixels, 1920 * 1080)
        self.assertEqual(wallpaper.filesize, len(fake_content))

        # 验证上传者关联被创建
        uploader_wallpaper = models.UploaderWallpaper.objects.get(wallpaper=wallpaper)
        self.assertEqual(uploader_wallpaper.uploader, uploader)

    @patch("wallpapers.services.misc.random_uploader")
    @patch("wallpapers.services.misc.download_url")
    @patch("PIL.Image.open")
    def test_create_wallpaper_duplicate(
        self, mock_pil_open, mock_download_url, mock_random_uploader
    ):
        """测试重复壁纸的情况"""
        # 模拟下载内容
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image.size = (1920, 1080)
        mock_pil_open.return_value = mock_image

        # 创建真实的上传者对象
        uploader = models.Uploader.objects.create(name="test_uploader")
        mock_random_uploader.return_value = uploader

        url = "https://example.com/test.jpg"

        # 第一次创建
        create_wallpaper(url)
        first_count = models.WallpaperImage.objects.count()

        # 第二次创建相同内容
        create_wallpaper(url)
        second_count = models.WallpaperImage.objects.count()

        # 验证没有重复创建
        self.assertEqual(first_count, second_count)

    @patch("wallpapers.services.misc.random_uploader")
    @patch("wallpapers.services.misc.download_url")
    @patch("PIL.Image.open")
    def test_create_wallpaper_success_original(
        self, mock_pil_open, mock_download_url, mock_random_uploader
    ):
        """测试成功创建壁纸（原始测试）"""
        # 模拟下载内容
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image.size = (1920, 1080)
        mock_pil_open.return_value = mock_image

        # 创建真实的上传者对象
        from wallpapers.models import Uploader

        mock_uploader = Uploader.objects.create(name="test_uploader")
        mock_random_uploader.return_value = mock_uploader

        # 执行任务
        test_url = "https://example.com/test.jpg"
        create_wallpaper(test_url)

        # 验证调用
        mock_download_url.assert_called_once_with(test_url)
        mock_pil_open.assert_called_once()

        # 验证数据库中创建了壁纸
        wallpaper = models.WallpaperImage.objects.get(url=test_url)
        self.assertEqual(wallpaper.format, "jpeg")
        self.assertEqual(wallpaper.width, 1920)
        self.assertEqual(wallpaper.height, 1080)
        self.assertEqual(wallpaper.aspect_ratio, 1920 / 1080)
        self.assertEqual(wallpaper.pixels, 1920 * 1080)
        self.assertEqual(wallpaper.filesize, len(fake_content))

        # 验证创建了上传者关联
        uploader_wallpaper = UploaderWallpaper.objects.get(wallpaper=wallpaper)
        self.assertEqual(uploader_wallpaper.uploader, mock_uploader)

    @patch("wallpapers.services.misc.download_url")
    def test_create_wallpaper_download_failed(self, mock_download_url):
        """测试下载失败的情况"""
        mock_download_url.return_value = None

        # 执行任务
        result = create_wallpaper("https://example.com/invalid.jpg")

        # 验证返回 None，且没有创建壁纸
        self.assertIsNone(result)
        self.assertEqual(models.WallpaperImage.objects.count(), 0)

    @patch("wallpapers.services.misc.random_uploader")
    @patch("wallpapers.services.misc.download_url")
    @patch("PIL.Image.open")
    def test_create_wallpaper_size_too_small(
        self, mock_pil_open, mock_download_url, mock_random_uploader
    ):
        """测试图片尺寸过小的情况"""
        # 模拟下载内容
        fake_content = b"fake_small_image"
        mock_download_url.return_value = fake_content

        # 模拟小尺寸图片
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image.size = (500, 300)  # 小于 720*1280 的最小要求
        mock_pil_open.return_value = mock_image

        # 执行任务
        result = create_wallpaper("https://example.com/small.jpg")

        # 验证返回 None，且没有创建壁纸
        self.assertIsNone(result)
        self.assertEqual(models.WallpaperImage.objects.count(), 0)

    @patch("wallpapers.services.misc.random_uploader")
    @patch("wallpapers.services.misc.download_url")
    @patch("PIL.Image.open")
    @patch("clip_service.api.encode_image_encode_image_post")
    @patch("wallpapers.services.qdrant.upsert_wallpaper_vector")
    def test_create_wallpaper_duplicate_md5_original(
        self,
        mock_upsert_wallpaper_vector: MagicMock,
        mock_clip_service: MagicMock,
        mock_pil_open,
        mock_download_url,
        mock_random_uploader,
    ):
        """测试重复 MD5 的情况（原始测试）"""
        # 模拟下载内容
        fake_content = b"fake_image_content"
        mock_download_url.return_value = fake_content

        # 模拟 CLIP 服务返回
        mock_clip_service.return_value = EncodingImageResponse(vector=[1.0, 2.0])

        # 模拟 PIL 图片对象
        mock_image = MagicMock()
        mock_image.format = "JPEG"
        mock_image.size = (1920, 1080)
        mock_pil_open.return_value = mock_image

        # 创建真实的上传者对象
        from wallpapers.models import Uploader

        mock_uploader = Uploader.objects.create(name="test_uploader")
        mock_random_uploader.return_value = mock_uploader

        # 先创建一个壁纸
        test_url1 = "https://example.com/test1.jpg"
        create_wallpaper(test_url1)

        mock_clip_service.assert_called_once_with(fake_content)
        mock_upsert_wallpaper_vector.assert_called_with(
            WallpaperImage.objects.first(), [1.0, 2.0]
        )

        # 再次创建相同内容的壁纸（不同URL但相同内容）
        mock_clip_service.reset_mock()
        mock_upsert_wallpaper_vector.reset_mock()

        test_url2 = "https://example.com/test2.jpg"
        create_wallpaper(test_url2)

        mock_clip_service.assert_not_called()
        mock_upsert_wallpaper_vector.assert_not_called()

        # 验证只创建了一个壁纸记录（因为 MD5 相同）
        self.assertEqual(models.WallpaperImage.objects.count(), 1)
        wallpaper = models.WallpaperImage.objects.first()
        # 应该保持第一个 URL
        assert wallpaper is not None
        self.assertEqual(wallpaper.url, test_url1)
