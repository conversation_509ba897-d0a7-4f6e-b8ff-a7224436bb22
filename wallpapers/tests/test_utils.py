import os
import types

import pytest

from wallpapers.utils.colors import ColorFamily, get_color_family
from wallpapers.utils.datastructures import ChainObject


class TestColors:
    def test_get_rgb_histogram(self):
        with open(
            os.path.join(os.path.dirname(__file__), "assets/photo-2099737.jpg"), "rb"
        ) as fp:
            assert get_color_family(fp) == ColorFamily.PINK


class TestChainObject:
    """测试 ChainObject 链式对象功能"""

    def test_basic_functionality(self):
        """测试基本功能：对象、字典访问和优先级"""
        # 使用 SimpleNamespace 创建测试对象
        obj1 = types.SimpleNamespace(name="first", unique1="obj1_only")
        obj2 = types.SimpleNamespace(name="second", unique2="obj2_only")
        data = {"name": "dict", "dict_key": "dict_value"}

        # 测试单个对象
        chain = ChainObject(obj1)
        assert chain.name == "first"
        assert chain.unique1 == "obj1_only"

        # 测试单个字典
        chain = ChainObject(data)
        assert chain.name == "dict"
        assert chain.dict_key == "dict_value"

        # 测试优先级：第一个对象优先
        chain = ChainObject(obj1, obj2, data)
        assert chain.name == "first"  # obj1 优先
        assert chain.unique1 == "obj1_only"
        assert chain.unique2 == "obj2_only"
        assert chain.dict_key == "dict_value"

    def test_error_handling(self):
        """测试错误处理"""
        obj = types.SimpleNamespace(existing="exists")
        chain = ChainObject(obj)

        # 正常访问
        assert chain.existing == "exists"

        # 不存在的属性抛出异常
        with pytest.raises(AttributeError):
            chain.nonexistent

        # 空对象
        empty_chain = ChainObject()
        with pytest.raises(AttributeError):
            empty_chain.any_attr

    def test_mixed_sources(self):
        """测试混合数据源"""
        config = types.SimpleNamespace(debug=False, timeout=30)
        user_settings = {"debug": True, "theme": "dark"}
        env_vars = {"timeout": 60, "api_key": "secret"}

        # 优先级：用户设置 > 环境变量 > 默认配置
        chain = ChainObject(user_settings, env_vars, config)

        assert chain.debug is True  # 用户设置优先
        assert chain.timeout == 60  # 环境变量覆盖默认
        assert chain.theme == "dark"
        assert chain.api_key == "secret"
