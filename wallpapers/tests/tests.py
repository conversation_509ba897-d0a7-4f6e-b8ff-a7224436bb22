from unittest import mock

import pytest
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.test import TestCase
from django.urls import reverse

from wallpapers import models
from wallpapers.auth import generate_access_token

User = get_user_model()


class TestAPIWithFixtures(TestCase):
    fixtures = ["test"]

    def test_wallpapers_resize(self):
        response = self.client.get(
            reverse("wallpapers-api:WallpaperListView") + "?size=12&size=aa"
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json(),
            {
                "errors": [
                    {"loc": [0], "msgs": ["Invalid value"]},
                    {"loc": [1], "msgs": ["Invalid value"]},
                ],
                "in": "query",
                "name": "size",
            },
        )

    def test_wallpapers(self):
        response = self.client.get(reverse("wallpapers-api:WallpaperListView"))
        # 只获取有上传者的壁纸
        assert response.json() == {
            "current_page": 1,
            "current_page_size": 10,
            "total": 2,
            "wallpapers": [
                {
                    "content_md5": "ea4d214b6d466d9a08ae20ba2c74d0be",
                    "filesize": 794385,
                    "format": "jpeg",
                    "height": 4000,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.pexels.com/photos/1270184/pexels-photo-1270184.jpeg",
                    },
                    "uploader": {
                        "name": "Tim",
                    },
                    "width": 6000,
                },
                {
                    "content_md5": "5437dc8c432e55886004b45a2834ceb8",
                    "filesize": 2050093,
                    "format": "jpeg",
                    "height": 6720,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.unsplash.com/photo-1749253894957-e95b399aa381",
                    },
                    "uploader": {
                        "name": "Sam",
                    },
                    "width": 4480,
                },
            ],
        }

    def test_wallpapers_related(self):
        response = self.client.get(
            reverse(
                "wallpapers-api:RelatedWallpapersAPI",
                kwargs={"key": "5437dc8c432e55886004b45a2834ceb8"},
            )
        )
        assert response.json() == {
            "related": [
                {
                    "content_md5": "ea4d214b6d466d9a08ae20ba2c74d0be",
                    "filesize": 794385,
                    "format": "jpeg",
                    "height": 4000,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/images.pexels.com/photos/1270184/pexels-photo-1270184.jpeg",
                    },
                    "uploader": {
                        "name": "Tim",
                    },
                    "width": 6000,
                },
            ],
        }


class TestClientTopicListAPI(TestCase):
    def test_get(self):
        client = models.Client.objects.create()
        topic = models.Topic.objects.create()
        models.ClientTopic.objects.create(topic=topic, client=client, title="untitle")
        response = self.client.get(
            reverse(
                "wallpapers-api:ClientTopicListAPI", kwargs={"client_id": client.pk}
            ),
        )
        assert response.status_code == 200
        assert response.json() == {
            "client_topics": [
                {
                    "id": 1,
                    "topic_id": 1,
                    "title": "untitle",
                }
            ]
        }


class TestClientTopicWallpaperListAPI(TestCase):
    def test_get(self):
        topic = models.Topic.objects.create()
        wallpaper = models.WallpaperImage.objects.create(
            content_md5="1234567890",
            url="https://example.com/image.jpg",
            format="jpeg",
            width=100,
            height=100,
            aspect_ratio=1.0,
            pixels=10000,
            filesize=1000,
        )
        models.TopicWallpaper.objects.create(topic=topic, wallpaper=wallpaper)
        client = models.Client.objects.create()
        client_topic = models.ClientTopic.objects.create(
            client=client, topic=topic, title="haha"
        )
        response = self.client.get(
            reverse(
                "wallpapers-api:ClientTopicWallpaperListAPI",
                kwargs={"topic_id": topic.pk, "client_id": client.pk},
            )
        )
        assert response.status_code == 200
        assert response.json() == {
            "client_topic": {
                "id": client_topic.pk,
                "topic_id": topic.pk,
                "title": "haha",
            },
            "wallpapers": [
                {
                    "content_md5": "1234567890",
                    "filesize": 1000,
                    "format": "jpeg",
                    "height": 100,
                    "width": 100,
                    "images": {
                        "default": "http://img.example.com/_/mb:51200/f:jpg/rs:fit:500:500/plain/https:/example.com/image.jpg",
                    },
                    "uploader": {
                        "name": "elephant31",
                    },
                },
            ],
        }


class TestDashToken(TestCase):
    @mock.patch("wallpapers.utils.cloudflare._verify_sync", return_value=True)
    def test_full(self, _):
        """测试全流程"""

        # 获取 access_token 和 refresh_token
        User.objects.create_superuser(username="admin", password="admin", email="")
        response = self.client.post(
            reverse("wallpapers-api:DashTokenAPI"),
            {"username": "admin", "password": "admin"},
            content_type="application/json",
            headers={"CF-Turnstile-Response": "123456"},
        )
        assert response.status_code == 200
        assert "access_token" in response.json()

        # 测试 refresh 接口
        self.client.cookies.load(response.cookies)
        response = self.client.get(
            reverse("wallpapers-api:DashRefreshTokenAPI"),
        )
        assert response.status_code == 200


class TestCaseWithJWTClient(TestCase):
    def setUp(self):
        from wallpapers.auth import generate_access_token

        user = User.objects.create_user(username="testuser", password="testpass123")
        access_token = generate_access_token(user.pk)
        self.client.defaults["HTTP_AUTHORIZATION"] = f"Bearer {access_token}"

        super().setUp()


class TestDashPublishTopicAPI(TestCaseWithJWTClient):
    def test_post(self):
        client = models.Client.objects.create()
        topic = models.Topic.objects.create()
        url = reverse(
            "wallpapers-api:DashPublishTopicAPI",
            kwargs={"topic_id": topic.pk, "client_id": client.id.hex},
        )
        response = self.client.post(
            url, {"title": "test"}, content_type="application/json"
        )
        assert response.status_code == 201
        response = self.client.post(
            url, {"title": "test"}, content_type="application/json"
        )
        assert response.status_code == 409


@pytest.mark.django_db
def test_required_jwt(rf):
    from users.models import Role, RoleAPIPermission
    from wallpapers.auth import required_jwt, user_permissions

    @required_jwt(permission="123")
    def view(request):
        return HttpResponse("OK")

    # 测试超级管理员
    super_user = User.objects.create_superuser(
        username="admin", password="admin", email=""
    )
    req = rf.get(
        "/", headers={"Authorization": f"Bearer {generate_access_token(super_user.pk)}"}
    )
    resp = view(req)
    assert resp.status_code == 200
    assert resp.content == b"OK"

    # 测试普通用户
    normal_user = User.objects.create_user(
        username="normal", password="normal", email=""
    )
    req = rf.get(
        "/",
        headers={"Authorization": f"Bearer {generate_access_token(normal_user.pk)}"},
    )
    resp = view(req)
    assert resp.status_code == 403

    # 为普通用户添加权限
    role = Role.objects.create(name="test")
    RoleAPIPermission.objects.create(role=role, operation_id="123")
    role.users.add(normal_user)
    user_permissions.clear_cache(normal_user)  # type: ignore
    resp = view(req)
    assert resp.status_code == 200
    assert resp.content == b"OK"
