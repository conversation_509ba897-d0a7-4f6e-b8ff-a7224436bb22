from django.conf import settings
from django.http import HttpRequest, JsonResponse
from django.shortcuts import redirect, render
from django.urls import include, path, reverse

from openapi.transformation import as_django_path, as_oas_path, as_path_item_object
from wallpapers.auth import OAS_SECURITY_KEY

from .apis import default as views
from .apis import rest
from .apis.base import APIView

rest_apis: list[tuple[str, type[APIView]]] = [
    ("/rest/wallpapers", rest.WallpaperListAPI),
    ("/rest/wallpapers/color/rgb/{rgb}", rest.WallpaperColorSearchAPI),
    ("/rest/wallpapers/{wallpaper_id}/uploaders", rest.WallpaperUploaderListAPI),
    ("/rest/wallpapers/{wallpaper_id}/similar", rest.WallpaperSimilarAPI),
    ("/rest/topics/{topic_id}/wallpapers", rest.TopicWallpaperListAPI),
    ("/rest/clients/{client_id}/clienttopics", rest.ClientClienttopicListAPI),
]

default_apis: list[tuple[str, type[APIView]]] = [
    ("/image/{key}", views.ImageAPI),
    ("/wallpapers", views.WallpaperListView),
    ("/wallpapers/{key}/related", views.RelatedWallpapersAPI),
    ("/categories/colors", views.ColorCategoryAPI),
    ("/categories/tags", views.TagCategoryAPI),
    ("/clients/{client_id}/topics", views.ClientTopicListAPI),
    (
        "/clients/{client_id}/topics/{topic_id}/wallpapers",
        views.ClientTopicWallpaperListAPI,
    ),
    ("/dash/auth/token", views.DashTokenAPI),
    ("/dash/auth/token/refresh", views.DashRefreshTokenAPI),
    ("/dash/auth/token/revoke", views.DashRevokeTokenAPI),
    ("/dash/stats", views.DashStatsAPI),
    ("/dash/userinfo", views.DashUserInfoAPI),
    ("/dash/uploadwallpapers", views.DashUploadWallapersAPI),
    ("/dash/topics", views.DashTopicListAPI),
    ("/dash/topics/{topic_id}", views.DashTopicDetailAPI),
    ("/dash/topics/{topic_id}/clienttopics", views.DashTopicClientTopicListAPI),
    ("/dash/topics/{topic_id}/wallpapers", views.DashTopicWallaperListAPI),
    (
        "/dash/topics/{topic_id}/wallpapers/{wallpaper_id}",
        views.DashTopicWallaperDetailAPI,
    ),
    ("/dash/clients", views.DashClientListAPI),
    ("/dash/clienttopics", views.DashClientTopicListAPI),
    ("/dash/clienttopics/{clienttopic_id}", views.DashClientTopicDetailAPI),
    ("/dash/topic/{topic_id}/publish/{client_id}", views.DashPublishTopicAPI),
    ("/dash/topics/{topic_id}/suggestions/tags", views.DashTopicSuggestedTagsAPI),
    ("/dash/search/wallpapers", views.DashSearchWallpapersAPI),
    ("/dash/search/wallpapers/v2", views.DashSearchWallpapersAPIV2),
    ("/dash/wallpapers", views.DashWallpaperListAPI),
    ("/dash/wallpapers/{wallpaper_id}/topics", views.DashWallpaperTopicListAPI),
    ("/dash/wallpapers/{wallpaper_id}/similar", views.DashWallpaperSimilarAPI),
]

apis = rest_apis + default_apis


def index(request):
    return redirect("wallpapers-api:apidoc")


def apidoc(request: HttpRequest):
    return render(
        request,
        "swagger-ui.html",
        context={
            "config": {"url": reverse("wallpapers-api:openapi")},
        },
    )


def openapi(request):
    oas = {
        "openapi": "3.0.3",
        "info": {
            "title": "Wallpapers API",
            "version": "0.1.0",
        },
        "paths": {as_oas_path(path): as_path_item_object(view) for path, view in apis},
        "components": {
            "securitySchemes": {
                OAS_SECURITY_KEY: {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT",
                }
            },
        },
        "x-json-schema-faker": {
            "locale": "zh",
            "min-items": 0,
            "max-items": 10,
        },  # https://docs.stoplight.io/docs/prism/9528b5a8272c0-dynamic-response-generation-with-faker
    }
    prefix = request.path.rpartition("/")[0]
    if prefix:
        oas.setdefault("servers", [{"url": prefix}])
    return JsonResponse(
        oas,
        json_dumps_params={"indent": 2 if settings.DEBUG else 0, "ensure_ascii": False},
    )


app_name = "wallpapers-api"
urlpatterns = [
    path("", index, name="index"),
    path("apidoc/", apidoc, name="apidoc"),
    path(
        "",
        include(
            [
                path("openapi.json", openapi, name="openapi"),
                *(
                    path(as_django_path(i[0])[1:], i[1].as_view(), name=i[1].__name__)
                    for i in apis
                ),
            ]
        ),
    ),
]
