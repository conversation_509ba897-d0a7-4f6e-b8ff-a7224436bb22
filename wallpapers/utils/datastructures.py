from collections.abc import Mapping


class ChainObject:
    """
    链式对象，用于从多个对象中查找属性
    """

    def __init__(self, *args) -> None:
        self.__objs = args

    def __getattr__(self, name):
        for obj in self.__objs:
            if isinstance(obj, Mapping):
                if name in obj:
                    return obj[name]
            else:
                if hasattr(obj, name):
                    return getattr(obj, name)
        raise AttributeError(name)
