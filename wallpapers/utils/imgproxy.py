import base64
import hashlib
import hmac
import typing
from urllib.parse import urljoin

import configat
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from django.utils.functional import SimpleLazyObject

_IMGPROXY_URL = typing.cast(
    str, SimpleLazyObject(lambda: configat.resolve("@env:IMGPROXY_URL"))
)
_IMGPROXY_KEY: bytes | None = configat.resolve(
    "@env:IMGPROXY_KEY", None, cast=bytes.fromhex
)
_IMGPROXY_SALT: bytes | None = configat.resolve(
    "@env:IMGPROXY_SALT", None, cast=bytes.fromhex
)
_IMGPROXY_SOURCE_URL_ENCRYPTION_KEY: bytes | None = configat.resolve(
    "@env:IMGPROXY_SOURCE_URL_ENCRYPTION_KEY", None, cast=bytes.fromhex
)


def get_imgproxy_url(source_url: str, width: int = 0, height: int = 0) -> str:
    """
    生成 imgproxy 的 URL

    如何同时设置 width 和 height，则取最小值进行等比例缩放。
    """
    parts = [
        "mb:" + str(50 * 1024),
        "f:jpg",
    ]

    # resize
    if width != 0 or height != 0:
        parts.append(f"rs:fit:{width}:{height}")

    if _IMGPROXY_SOURCE_URL_ENCRYPTION_KEY:
        parts.extend(
            [
                "enc",
                _aes_encrypt(source_url.encode(), _IMGPROXY_SOURCE_URL_ENCRYPTION_KEY),
            ]
        )
    else:
        parts.extend(["plain", source_url])
    path = "/" + "/".join(parts)

    signature = _get_signture(path)

    return urljoin(_IMGPROXY_URL, f"{signature}{path}")


def _get_signture(path: str) -> str:
    if _IMGPROXY_KEY is None or _IMGPROXY_SALT is None:
        return "_"
    digest = hmac.new(
        _IMGPROXY_KEY,
        msg=_IMGPROXY_SALT + path.encode(),
        digestmod=hashlib.sha256,
    ).digest()
    return base64.urlsafe_b64encode(digest).rstrip(b"=").decode()


def _aes_encrypt(plain_text: bytes, key: bytes) -> str:
    if len(key) not in {16, 24, 32}:
        raise ValueError("Key must be either 16, 24, or 32 bytes long")

    # iv = os.urandom(AES.block_size)
    # 固定 iv，使相同的 URL 结果一致，否则 CDN/缓存就没用了
    iv = b"\xd5\xbb\x98\xd9\xbe\xc9K\x8f\xc2\xd9\\*\xc7v\x98y"

    # 使用 CBC 模式
    cipher = AES.new(key, AES.MODE_CBC, iv)

    # PKCS7 填充
    padded = pad(plain_text, AES.block_size)

    # 加密
    cipher_text = cipher.encrypt(padded)

    # 拼接 IV 和密文
    final = iv + cipher_text

    # Base64 URL 编码（不带等号 padding）
    encoded = base64.urlsafe_b64encode(final).rstrip(b"=")
    return encoded.decode("utf-8")
