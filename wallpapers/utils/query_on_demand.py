"""实现按需查询"""

from __future__ import annotations

import copy
from typing import Literal

import zangar as z


class _ModelMeta(type):
    def __init__(cls, name, bases: tuple[type], attrs: dict):
        super().__init__(name, bases, attrs)
        cls.__fields__: dict[str, _FieldBase] = {}

        # 首先收集所有父类的字段
        for base in bases:
            if isinstance(base, _ModelMeta):
                for k, v in base.__fields__.items():
                    # 直接使用父类字段，因为类字段是模板不会被修改
                    cls.__fields__[k] = v

        # 然后添加当前类的字段（会覆盖同名的父类字段）
        for k, v in attrs.items():
            if isinstance(v, _FieldBase):
                cls.__fields__[k] = v
                v.__metadata__.name = k


class _Metadata:
    # 为避免污染字段属性，使用元数据的方式来管理字段属性
    def __init__(self, *, include_in_keys: bool):
        self.name: str
        self.parent: _Metadata | None = None
        self.schema: z.Schema | z.field
        self.include_in_key: bool = include_in_keys

    def paths(self) -> list[str]:
        if self.parent is None:
            return [self.name]
        return [*self.parent.paths(), self.name]

    def fullname(self) -> str:
        return ".".join(self.paths())


class _FieldBase:
    def __init__(self, *, include_in_keys=True):
        # 每个实例化的字段拥有一个元数据对象，用于存放字段的元数据，
        # 防止属性直接设置在字段中，造成污染。
        self.__metadata__ = _Metadata(include_in_keys=include_in_keys)


class Field(_FieldBase):
    def __init__(self, schema: z.Schema | z.field, include_in_keys=True):
        super().__init__(include_in_keys=include_in_keys)
        self.__metadata__.schema = schema


def _setup_parent_relationships(model_instance: "Model"):
    """递归地为 Model 实例的所有字段设置正确的父级关系"""
    self_fields = {}
    for child_name, child_field in model_instance.__class__.__fields__.items():
        # 创建一个新的字段副本，设置正确的父级关系
        child_copy = copy.copy(child_field)
        child_copy.__metadata__ = copy.copy(child_field.__metadata__)
        child_copy.__metadata__.parent = model_instance.__metadata__
        setattr(model_instance, child_name, child_copy)
        self_fields[child_name] = child_copy

        # 如果子字段也是 Model，递归设置其子字段的父级关系
        if isinstance(child_copy, Model):
            _setup_parent_relationships(child_copy)
    model_instance.__fields__ = self_fields


class Model(_FieldBase, metaclass=_ModelMeta):
    def __init__(self, *, many=False, **kwargs) -> None:
        super().__init__(**kwargs)
        self._many = many
        _setup_parent_relationships(self)


def flatten(model: type[Model]) -> list[str]:
    def inner(model: type[Model] | Model):
        for name, field in model.__fields__.items():
            if isinstance(field, Field):
                yield field.__metadata__.fullname()
            elif isinstance(field, Model):
                yield from inner(field)

    return list(inner(model))


class FieldSelector:
    def __init__(self, flattened: list[str]):
        self.__map = {}
        for field in flattened:
            parts = field.split(".")
            current = self.__map
            for part in parts:
                current = current.setdefault(part, {})

    def contains(self, field: _FieldBase) -> bool:
        keys = field.__metadata__.paths()
        current = self.__map
        for key in keys:
            if key not in current:
                return False
            current = current[key]
        return True

    def keys(self, model: type[Model] | Model) -> list[str]:
        current = self.__map
        if isinstance(model, Model):
            keys = model.__metadata__.paths()
            for key in keys:
                if key not in current:
                    return []
                current = current[key]

        rv = []
        for k in current:
            if model.__fields__[k].__metadata__.include_in_key:
                rv.append(k)
        return rv


def struct(
    model: type[Model],
    fields: FieldSelector | None = None,
    *,
    optional: bool = False,
    usefor: Literal["write", None, "readself"] = None,
):
    def inner(current_model: type[Model], current_instance: Model | None = None):
        zangar_fields = {}

        for name, field in current_model.__fields__.items():
            # 创建字段实例以便检查路径
            if current_instance is not None:
                # 我们在嵌套层级中，使用实例字段
                field_to_check = getattr(current_instance, name)
            else:
                # 我们在顶层，使用类字段
                field_to_check = getattr(model, name)

            # 检查是否应该包含这个字段
            should_include = fields is None or fields.contains(field_to_check)

            if should_include:
                if isinstance(field, Field):
                    schema = field.__metadata__.schema
                    if isinstance(schema, z.field):
                        if usefor == "write" and schema.meta.get("readonly", False):
                            continue
                    zangar_fields[name] = schema
                elif isinstance(field, Model):
                    if usefor == "write" or usefor == "readself":
                        continue
                    # 递归处理嵌套的 Model
                    nested_fields = inner(field.__class__, field_to_check)
                    if nested_fields:
                        if field._many:
                            zangar_fields[name] = z.to.list(z.struct(nested_fields))
                        else:
                            zangar_fields[name] = z.struct(nested_fields)

        if optional:
            zangar_fields = z.optional_fields(zangar_fields)
        return zangar_fields

    return z.struct(inner(model))
